/** @format */

// controllers/brand/authController.js

const { Brand } = require('../../database/index');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { sendBrandSignupEmail, sendBrandTempPassword } = require('../../functions/sendEmail');
const secret = process.env.SECRET_KEY;

// Brand Signup
const signup = async (req, res) => {
    // Send signup acknowledgment email asynchronously
    sendBrandSignupEmail(req.body.email, req.body.companyName);
	try {
		const {
			companyName,
			contactName,
			email,
			website,
			password,
		} = req.body;

		// Block public email domains

		const publicDomains = ['gmail.com', 'yahoo.com', 'hotmail.com'];
		const domain = email.split('@')[1];
		if (publicDomains.includes(domain)) {
			return res.status(400).json({
				status: 'failed',
				message:
					'Only business emails are allowed. Contact <EMAIL> for manual onboarding.',
			});
		}

		const existing = await Brand.findOne({ email });
		if (existing) {
			return res
				.status(400)
				.json({ status: 'failed', message: 'Email already exists' });
		}

		const hashed = await bcrypt.hash(password, 10);

		const brand = await Brand.create({
			companyName,
			contactName,
			email,
			website,
			password: hashed,
			isVerified: false,
		});

		return res.status(201).json({
			status: 'success',
			message: 'Signup submitted. Please wait for admin approval.',
		});
	} catch (err) {
		console.error('Brand signup error:', err);
		return res.status(500).json({ status: 'failed', message: 'Server error' });
	}
};

// Brand Signin
const signin = async (req, res) => {
	try {
		const { email, password } = req.body;
		const brand = await Brand.findOne({ email });

		if (!brand) {
			return res
				.status(404)
				.json({ status: 'failed', message: 'Brand not found' });
		}

		if (!brand.isVerified) {
			return res
				.status(403)
				.json({ status: 'failed', message: 'Brand not approved yet by admin' });
		}

		const isMatch = await bcrypt.compare(password, brand.password);
		if (!isMatch) {
			return res
				.status(401)
				.json({ status: 'failed', message: 'Incorrect password' });
		}

		const token = jwt.sign({ _id: brand._id, role: 'brand' }, secret, {
			expiresIn: '24h',
		});
		return res.json({
			status: 'success',
			message: 'Logged in successfully',
			token,
			brand: {
				companyName: brand.companyName,
				email: brand.email,
				id: brand._id,
			},
		});
	} catch (err) {
		console.error('Brand signin error:', err);
		return res.status(500).json({ status: 'failed', message: 'Server error' });
	}
};

// Brand Verify Token
const verifyAuth = async (req, res) => {
	try {
		const token = req.headers.authorization?.split(' ')[1];
		if (!token) {
			return res
				.status(401)
				.json({ status: 'failed', message: 'Token missing' });
		}

		const decoded = jwt.verify(token, secret);
		const brand = await Brand.findById(decoded._id);

		if (!brand || !brand.isVerified) {
			return res
				.status(403)
				.json({ status: 'failed', message: 'Unauthorized' });
		}

		return res.json({
			status: 'success',
			brand: {
				id: brand._id,
				email: brand.email,
				companyName: brand.companyName,
			},
		});
	} catch (err) {
		return res
			.status(401)
			.json({ status: 'failed', message: 'Invalid or expired token' });
	}
};

// Middleware for protecting brand routes
const verifyBrandToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(" ")[1];
    if (!token) {
      return res.status(401).json({ status: "failed", message: "Token missing" });
    }

    const decoded = jwt.verify(token, secret);
    const brand = await Brand.findById(decoded._id); // ✅ fetch full brand

    if (!brand) {
      return res.status(401).json({ status: "failed", message: "Brand not found" });
    }

    req.user = brand; // ✅ attach full brand object (including _id)
    next();
  } catch (err) {
    return res
      .status(401)
      .json({ status: "failed", message: "Invalid or expired token" });
  }
};



const forgotPassword = async (req, res) => {
	const { email } = req.body;
	const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
	const userAgent = req.get("User-Agent");

	if (!email) {
		return res.json({ status: "failed", message: "Email is required" });
	}

	try {
		const brand = await Brand.findOne({ email: email.trim() });

		if (!brand) {
			// ✅ Generic response (even if not found)
			return res.json({
				status: "success",
				message: "If this email exists, we've sent a temporary password.",
			});
		}

		// ✅ Generate and hash temporary password
		const tempPassword = Math.random().toString(36).slice(-8);
		const hashedTemp = await bcrypt.hash(tempPassword, 10);

		brand.password = hashedTemp;
		await brand.save();

		// ✅ Send email with temporary password
		await sendBrandTempPassword(brand.email, tempPassword, brand.companyName);

		return res.json({
			status: "success",
			message: "If this email exists, we've sent a temporary password.",
		});
	} catch (err) {
		console.error("Brand forgot password error:", err.message);
		res.status(500).json({ status: "failed", message: "Something went wrong" });
	}
};

module.exports = {
	signup,
	signin,
	verifyAuth,
	verifyBrandToken,
	forgotPassword,
};
