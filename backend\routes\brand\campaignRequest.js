const express = require('express');
const {
  requestNewCampaign,
  getMyCampaignRequests,
  getCampaignRequestById,
  updateCampaignRequest,
  deleteCampaignRequest
} = require('../../middlewares/brand/campaignRequestController');
const { verifyBrandToken } = require('../../middlewares/brand/authController');

const router = express.Router();

// Create a new campaign request
router.post('/request', verifyBrandToken, requestNewCampaign);

// Get all campaign requests for the authenticated brand
router.get('/my-requests', verifyBrandToken, getMyCampaignRequests);

// Get a single campaign request by ID
router.get('/request/:id', verifyBrandToken, getCampaignRequestById);

// Update a pending campaign request
router.patch('/request/:id', verifyBrandToken, updateCampaignRequest);

// Delete (withdraw) a pending campaign request
router.delete('/request/:id', verifyBrandToken, deleteCampaignRequest);

module.exports = router;
