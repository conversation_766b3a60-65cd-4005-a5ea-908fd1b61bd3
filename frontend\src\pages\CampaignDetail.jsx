import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import useAuthStore from "../state/atoms";
import { useNavigate } from "react-router-dom";
import config from "../config";
import axios from "axios";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import SubmitApplication from "../components/application/submitApplication";
import SuccessPopup from "../components/successPopup";
import { Helmet } from "react-helmet";
import Cookies from "js-cookie";
import { FaChevronDown, FaChevronUp, FaCopy, FaExternalLinkAlt } from "react-icons/fa";

const CampaignDetail = () => {
  const [loading, setloading] = useState(true);
  const [campaign, setCampaign] = useState({});
  const Navigate = useNavigate();
  const { isLogin, User } = useAuthStore();
  const { campaignId } = useParams();
  const [isOpen, setIsOpen] = useState(false);
  const [success, setSuccess] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [submittedUrls, setSubmittedUrls] = useState(null);
  const [instagramUrls, setInstagramUrls] = useState("");
  const [youtubeUrls, setYoutubeUrls] = useState("");
  const [tiktokUrls, setTiktokUrls] = useState("");
  const [allowReuse, setAllowReuse] = useState(false);
  const [appliedStatus, setAppliedStatus] = useState(""); // "Approved", "Rejected", "Pending", ""
  const [applicantsCount, setApplicantsCount] = useState(0);
  const [appliedThisMonth, setAppliedThisMonth] = useState(0);
  const [campaignStatus, setCampaignStatus] = useState("Recruiting"); // or "Closed"
  
  // Accordion state management
  const [accordionState, setAccordionState] = useState({
    campaignOverview: true,    // ✅ Open by default
    contentGuidelines: true,   // ✅ Open by default
    productInfo: false,        // 🔽 Collapsed by default
    pricingContent: false,     // 🔽 Collapsed by default (paid only)
    productDetails: false      // 🔽 Collapsed by default
  });

  // Helper function to get category badge colors
  const getCategoryBadgeColor = (category) => {
    const colors = {
      'Beauty': 'bg-pink-500 text-white',
      'Food': 'bg-orange-500 text-white',
      'Beverage': 'bg-blue-500 text-white',
      'Wellness & Supplements': 'bg-green-500 text-white',
      'Personal Care': 'bg-purple-500 text-white',
    };
    return colors[category] || 'bg-gray-500 text-white';
  };

  // Toggle accordion section
  const toggleAccordion = (section) => {
    setAccordionState(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Copy content to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  useEffect(() => {
    if (appliedStatus === "Approved") {
      fetchSubmittedData();
    }
  }, [appliedStatus]);

  async function fetchSubmittedData() {
    try {
      const token = Cookies.get("token") || localStorage.getItem("token");
      const res = await axios.get(
        `${config.BACKEND_URL}/user/campaign-submission/${campaignId}`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (res.data.status === "success") {
        setSubmittedUrls({
          instagram: res.data.data.instagram_urls,
          youtube: res.data.data.youtube_urls,
          tiktok: res.data.data.tiktok_urls,
        });
      }
    } catch (err) {
      console.error(
        "Error fetching submitted content:",
        err.response?.data || err.message
      );
    }
  }

  const handleSubmitContent = async () => {
    const token = Cookies.get("token") || localStorage.getItem("token");
    try {
      const res = await axios.post(
        `${config.BACKEND_URL}/user/campaign-submission`,
        {
          campaign_id: campaignId,
          email: User.email,
          instagram_urls: instagramUrls.split(",").map((url) => url.trim()),
          youtube_urls: youtubeUrls.split(",").map((url) => url.trim()),
          tiktok_urls: tiktokUrls.split(",").map((url) => url.trim()),
          allow_brand_reuse: allowReuse,
        },
        {
          headers: {
            Authorization: token,
          },
        }
      );

      alert("Content submitted successfully!");

      // Show URLs after submit
      setSubmittedUrls({
        instagram: instagramUrls.split(",").map((url) => url.trim()),
        youtube: youtubeUrls.split(",").map((url) => url.trim()),
        tiktok: tiktokUrls.split(",").map((url) => url.trim()),
      });

      setShowSubmitModal(false); // close modal
    } catch (error) {
      console.error("Submission failed:", error);
      alert("Something went wrong while submitting.");
    }
  };

  useEffect(() => {
    if (User?.email && campaignId) {
      getUserApplicationStatus();
    }
  }, [User, campaignId]);

  async function getUserApplicationStatus() {
    try {
      const token = Cookies.get("token") || localStorage.getItem("token");
      const res = await axios.get(
        `${config.BACKEND_URL}/user/campaigns/appliedCampaigns`,
        {
          headers: {
            authorization: token,
          },
        }
      );

      if (res.data.status === "success") {
        setAppliedThisMonth(res.data.appliedThisMonth || 0);
        const found = res.data.campaigns.find(
          (c) => String(c.id) === String(campaignId)
        );
        if (found) {
          setAppliedStatus(found.applicationStatus);
        }
      }
    } catch (err) {
      console.error("Error fetching applied status:", err);
    }
  }

  useEffect(() => {
    getCampaign();
  }, [campaignId]);

  async function getCampaign() {
    try {
      const res = await axios.get(
        `${config.BACKEND_URL}/user/campaigns/${campaignId}/${User.email}`
      );
      if (res.data.status === "success") {
        console.log(res.data.campaign);
        const c = res.data.campaign;
        setCampaign(c);
        setApplicantsCount(res.data.applicantsCount || 0);
        setCampaignStatus(res.data.campaignStatus || "Recruiting");
      }
    } catch {
      // error handling
    } finally {
      setloading(false);
    }
  }

  const handleOutsideClick = (e) => {
    if (e.target.id === "sidebar-overlay") {
      setIsOpen(false);
    }
  };

  let metaTags = [];

  if (campaign && !loading) {
    metaTags = [
      <meta name="robots" content="index, follow" key="robots" />,
      <meta
        property="og:title"
        content={campaign.campaignTitle || "N/A"}
        key="og-title"
      />,
      <meta
        property="og:description"
        content={campaign.productDescription || "N/A"}
        key="og-description"
      />,
    ];
  } else {
    metaTags = [
      <meta name="robots" content="noindex, nofollow" key="robots" />,
    ];
  }

  function toCamelCase(str) {
    if (!str) return "";
    return str
      .replace(/[^a-zA-Z0-9 ]/g, "") // Remove special characters
      .trim()
      .split(/\s+/) // Split by whitespace
      .map((word, index) =>
        index === 0
          ? word.toLowerCase()
          : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      )
      .join("");
  }

  return (
    <div className="min-h-screen bg-black">
      <Helmet>
        <title>{campaign.campaignTitle || "Campaign Details"}</title>
        {metaTags}
      </Helmet>
      
      {loading ? (
        <CampaignSkeleton />
      ) : (
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* 1️⃣ Campaign Overview Section - Always Open */}
          <div className="bg-[#1a1a1a] rounded-lg mb-4 border border-gray-800">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                {campaign.brandLogo && (
                  <img
                    src={campaign.brandLogo}
                    alt="Brand Logo"
                    className="w-12 h-12 rounded-full object-cover"
                  />
                )}
                <div>
                  <h1 className="text-2xl font-bold text-white">{campaign.campaignTitle || "N/A"}</h1>
                  <p className="text-gray-400">{campaign.brandName || "N/A"}</p>
                </div>
                {campaign.campaignIndustry && (
                  <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getCategoryBadgeColor(campaign.campaignIndustry)}`}>
                    {campaign.campaignIndustry}
                  </span>
                )}
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Product:</span>
                  <p className="text-white font-medium">{campaign.productName || campaign.campaignTitle || "N/A"}</p>
                </div>
                <div>
                  <span className="text-gray-400">Platform:</span>
                  <p className="text-white font-medium">
                    {Array.isArray(campaign.platforms) ? campaign.platforms.join(", ") : (campaign.platforms || campaign.platform || "N/A")}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">Deadline:</span>
                  <p className="text-white font-medium">
                    {campaign.deadline ? campaign.deadline.split("T")[0] : "N/A"}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">Type:</span>
                  <p className="text-white font-medium">
                    {campaign.campaignType === "paid" ? "Paid" : "Gifted"}
                  </p>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2 mt-4">
                <span className="inline-flex items-center text-sm text-white bg-[#333] px-3 py-1 rounded-full">
                  🎁 Free Product
                </span>
                {campaign.paymentAmount && Number(campaign.paymentAmount) > 0 && (
                  <span className="inline-flex items-center text-sm text-white bg-[#333] px-3 py-1 rounded-full">
                    💵 Paid: ${campaign.paymentAmount}
                  </span>
                )}
                <span className="inline-flex items-center text-sm text-white bg-[#333] px-3 py-1 rounded-full">
                  📌 {campaign.contentFormat && campaign.contentFormat.length > 0 ? `${campaign.contentFormat.length} Video${campaign.contentFormat.length > 1 ? 's' : ''} Required` : '1 Video Required'}
                </span>
              </div>
            </div>
          </div>

          {/* 2️⃣ Content Guidelines Section - Always Open */}
          <div className="bg-[#1a1a1a] rounded-lg mb-4 border border-gray-800">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-white">Content Guidelines</h2>
                <button
                  className="bg-[#333] text-white px-3 py-1 rounded hover:bg-[#444] text-sm font-medium flex items-center gap-2"
                  onClick={() => {
                    const hashtags = (campaign.requiredHashtags && campaign.requiredHashtags.length > 0) ? campaign.requiredHashtags.join(' ') : '';
                    const mention = campaign.mentionHandle || '';
                    const text = [hashtags, mention].filter(Boolean).join(' ');
                    copyToClipboard(text);
                  }}
                >
                  <FaCopy className="w-3 h-3" />
                  Copy All
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-gray-400 text-sm">Required Hashtags:</span>
                  <p className="text-white font-medium">
                    {campaign.requiredHashtags && campaign.requiredHashtags.length > 0 ? campaign.requiredHashtags.join(' ') : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400 text-sm">@Mention:</span>
                  <p className="text-white font-medium">{campaign.mentionHandle || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-gray-400 text-sm">Tone:</span>
                  <p className="text-white font-medium">{campaign.toneMessage || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-gray-400 text-sm">Avoid:</span>
                  <p className="text-white font-medium">{campaign.avoidMessage || 'N/A'}</p>
                </div>
              </div>
              
              {/* Reference Content */}
              {(campaign.referenceContentThumbnails?.length > 0 || campaign.referenceContentLinks?.length > 0) && (
                <div className="mt-4">
                  <span className="text-gray-400 text-sm">Reference Content:</span>
                  <div className="flex flex-wrap gap-3 mt-2">
                    {campaign.referenceContentThumbnails?.map((thumb, idx) => (
                      <img key={idx} src={thumb} alt="Reference" className="w-16 h-16 object-cover rounded" />
                    ))}
                    {campaign.referenceContentLinks?.map((link, idx) => (
                      <a key={idx} href={link} target="_blank" rel="noopener noreferrer" className="text-blue-400 underline text-sm flex items-center gap-1">
                        External Link {idx + 1}
                        <FaExternalLinkAlt className="w-3 h-3" />
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 3️⃣ Product Info & Images Section - Collapsed by Default */}
          <div className="bg-[#1a1a1a] rounded-lg mb-4 border border-gray-800">
            <button
              className="w-full p-6 text-left flex items-center justify-between hover:bg-[#222] transition-colors"
              onClick={() => toggleAccordion('productInfo')}
            >
              <h2 className="text-xl font-bold text-white">Product Info & Images</h2>
              {accordionState.productInfo ? (
                <FaChevronUp className="text-gray-400 w-5 h-5" />
              ) : (
                <FaChevronDown className="text-gray-400 w-5 h-5" />
              )}
            </button>
            
            {accordionState.productInfo && (
              <div className="px-6 pb-6 border-t border-gray-800">
                <div className="pt-4 space-y-4">
                  <div>
                    <span className="text-gray-400 text-sm">Highlights:</span>
                    <p className="text-white font-medium">{campaign.productHighlights || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="text-gray-400 text-sm">How to Use:</span>
                    <p className="text-white font-medium">{campaign.howToUse || 'N/A'}</p>
                  </div>
                  
                  {/* Product Images */}
                  {campaign.productImages && campaign.productImages.length > 0 ? (
                    <div>
                      <span className="text-gray-400 text-sm">Product Images:</span>
                      <div className="mt-2">
                        <Swiper
                          modules={[Navigation, Pagination]}
                          navigation
                          pagination={{ clickable: true }}
                          spaceBetween={10}
                          slidesPerView={1}
                          className="h-64"
                        >
                          {campaign.productImages.map((image, index) => (
                            <SwiperSlide key={index}>
                              <img
                                src={image}
                                alt={`Product ${index + 1}`}
                                className="w-full h-full object-contain rounded-lg"
                              />
                            </SwiperSlide>
                          ))}
                        </Swiper>
                      </div>
                    </div>
                  ) : (
                    <div className="text-gray-400">No images available.</div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 4️⃣ Pricing & Required Content Section - Collapsed by Default (Paid only) */}
          {campaign.campaignType === "paid" && (
            <div className="bg-[#1a1a1a] rounded-lg mb-4 border border-gray-800">
              <button
                className="w-full p-6 text-left flex items-center justify-between hover:bg-[#222] transition-colors"
                onClick={() => toggleAccordion('pricingContent')}
              >
                <h2 className="text-xl font-bold text-white">Pricing & Required Content</h2>
                {accordionState.pricingContent ? (
                  <FaChevronUp className="text-gray-400 w-5 h-5" />
                ) : (
                  <FaChevronDown className="text-gray-400 w-5 h-5" />
                )}
              </button>
              
              {accordionState.pricingContent && (
                <div className="px-6 pb-6 border-t border-gray-800">
                  <div className="pt-4 space-y-4">
                    <div>
                      <span className="text-gray-400 text-sm">Payment Amount:</span>
                      <p className="text-white font-medium">${campaign.paymentAmount || '0'}</p>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Content Format:</span>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {campaign.contentFormat?.map((content, index) => (
                          <span key={index} className="bg-[#333] text-white px-3 py-1 rounded-full text-sm">
                            {content || "N/A"}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">What You Will Get:</span>
                      <p className="text-white font-medium">{campaign.influencersReceive || "N/A"}</p>
                    </div>
                    <div>
                      <span className="text-gray-400 text-sm">Requirements:</span>
                      <p className="text-white font-medium">{campaign.participationRequirements || "N/A"}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 5️⃣ View Product Details Section - Collapsed by Default */}
          <div className="bg-[#1a1a1a] rounded-lg mb-4 border border-gray-800">
            <button
              className="w-full p-6 text-left flex items-center justify-between hover:bg-[#222] transition-colors"
              onClick={() => toggleAccordion('productDetails')}
            >
              <h2 className="text-xl font-bold text-white">View Product Details</h2>
              {accordionState.productDetails ? (
                <FaChevronUp className="text-gray-400 w-5 h-5" />
              ) : (
                <FaChevronDown className="text-gray-400 w-5 h-5" />
              )}
            </button>
            
            {accordionState.productDetails && (
              <div className="px-6 pb-6 border-t border-gray-800">
                <div className="pt-4 space-y-4">
                  <div>
                    <span className="text-gray-400 text-sm">Product Description:</span>
                    <p className="text-white font-medium">{campaign.productDescription || "N/A"}</p>
                  </div>
                  <div>
                    <span className="text-gray-400 text-sm">Category:</span>
                    <p className="text-white font-medium">{campaign.campaignIndustry || "N/A"}</p>
                  </div>
                  {/* Add more product details as needed */}
                </div>
              </div>
            )}
          </div>

          {/* 🔘 Apply to Campaign Button - Always Visible */}
          <div className="sticky bottom-4 z-10">
            <div className="bg-[#1a1a1a] rounded-lg border border-gray-800 p-4">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="text-center sm:text-left">
                  <p className="text-white font-medium">
                    {appliedStatus === "Approved"
                      ? "✅ Approved"
                      : appliedStatus === "Rejected"
                      ? "❌ Rejected"
                      : appliedStatus === "Pending"
                      ? "⏳ Pending Review"
                      : campaignStatus === "Closed"
                      ? "🔒 Campaign Closed"
                      : appliedThisMonth >= 5
                      ? "🚫 Monthly Limit Reached"
                      : "Ready to Apply"}
                  </p>
                  {appliedStatus === "Approved" && (
                    <Link to={`/AddPostUrl/${campaignId}`}>
                      <button className="bg-green-600 text-white font-semibold px-6 py-2 rounded-lg hover:bg-green-700 mt-2">
                        Submit Content
                      </button>
                    </Link>
                  )}
                </div>
                
                <button
                  onClick={() => {
                    if (!isLogin) {
                      Navigate("/signin");
                      return;
                    }
                    setIsOpen(true);
                  }}
                  disabled={
                    !!appliedStatus ||
                    campaignStatus === "Closed" ||
                    appliedThisMonth >= 5
                  }
                  className={`w-full sm:w-auto flex justify-center items-center font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform
                    ${
                      appliedStatus === "Approved"
                        ? "bg-green-600 text-white cursor-default"
                        : appliedStatus === "Rejected"
                        ? "bg-red-600 text-white cursor-default"
                        : appliedStatus === "Pending"
                        ? "bg-yellow-400 text-black cursor-default"
                        : "text-black bg-[#facc15] hover:bg-[#ffb703] hover:scale-105 hover:shadow-lg"
                    } shadow-md`}
                >
                  {appliedStatus === "Approved"
                    ? "Approved"
                    : appliedStatus === "Rejected"
                    ? "Rejected"
                    : appliedStatus === "Pending"
                    ? "Pending"
                    : campaignStatus === "Closed"
                    ? "Campaign Closed"
                    : appliedThisMonth >= 5
                    ? "Limit Reached"
                    : "Apply to Campaign"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Content Submission Modal */}
      {showSubmitModal && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-[10000]">
          <div className="bg-[#1e1e1e] p-6 rounded-lg max-w-[500px] w-full shadow-xl">
            <h3 className="text-white text-xl font-semibold mb-4">
              Submit Your Content
            </h3>

            <label className="text-white block mb-1">
              Instagram URLs (comma-separated)
            </label>
            <input
              type="text"
              className="w-full p-2 mb-3 rounded bg-black/40 text-white border border-gray-500"
              value={instagramUrls}
              onChange={(e) => setInstagramUrls(e.target.value)}
            />

            <label className="text-white block mb-1">
              YouTube URLs (comma-separated)
            </label>
            <input
              type="text"
              className="w-full p-2 mb-3 rounded bg-black/40 text-white border border-gray-500"
              value={youtubeUrls}
              onChange={(e) => setYoutubeUrls(e.target.value)}
            />

            <label className="text-white block mb-1">
              TikTok URLs (comma-separated)
            </label>
            <input
              type="text"
              className="w-full p-2 mb-3 rounded bg-black/40 text-white border border-gray-500"
              value={tiktokUrls}
              onChange={(e) => setTiktokUrls(e.target.value)}
            />

            <label className="text-white flex items-center gap-2 mb-4">
              <input
                type="checkbox"
                checked={allowReuse}
                onChange={(e) => setAllowReuse(e.target.checked)}
              />
              I authorize the brand to reuse my submitted content.
            </label>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowSubmitModal(false)}
                className="text-white px-4 py-2 rounded bg-gray-600 hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitContent}
                className="bg-yellow-400 text-black px-4 py-2 rounded hover:bg-yellow-500 font-semibold"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Overlay and Modals */}
      {isOpen && (
        <div
          id="sidebar-overlay"
          className="fixed inset-0 bg-[#0000008a] bg-opacity-50 z-9999"
          onClick={handleOutsideClick}
        ></div>
      )}
      
      <SubmitApplication
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        campaignId={campaignId}
        setSuccess={setSuccess}
      />
      
      <SuccessPopup
        show={success}
        onClose={() => {
          setAppliedStatus("Pending");
          setCampaign((prev) => ({ ...prev, applied: true }));
          setSuccess(false);
        }}
      />
    </div>
  );
};

const CampaignSkeleton = () => {
  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="space-y-4">
        {/* Campaign Overview Skeleton */}
        <div className="bg-[#1a1a1a] rounded-lg p-6 border border-gray-800 animate-pulse">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gray-700 rounded-full"></div>
            <div className="flex-1">
              <div className="h-6 bg-gray-700 rounded w-2/3 mb-2"></div>
              <div className="h-4 bg-gray-700 rounded w-1/3"></div>
            </div>
            <div className="w-20 h-6 bg-gray-700 rounded-full"></div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i}>
                <div className="h-3 bg-gray-700 rounded w-1/2 mb-1"></div>
                <div className="h-4 bg-gray-700 rounded w-full"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Content Guidelines Skeleton */}
        <div className="bg-[#1a1a1a] rounded-lg p-6 border border-gray-800 animate-pulse">
          <div className="h-6 bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i}>
                <div className="h-3 bg-gray-700 rounded w-1/3 mb-1"></div>
                <div className="h-4 bg-gray-700 rounded w-full"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Accordion Sections Skeleton */}
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-[#1a1a1a] rounded-lg border border-gray-800 animate-pulse">
            <div className="p-6">
              <div className="h-6 bg-gray-700 rounded w-1/2"></div>
            </div>
          </div>
        ))}

        {/* Apply Button Skeleton */}
        <div className="bg-[#1a1a1a] rounded-lg border border-gray-800 p-4 animate-pulse">
          <div className="h-12 bg-gray-700 rounded w-full"></div>
        </div>
      </div>
    </div>
  );
};

export default CampaignDetail;
