const express = require("express");
const { Campaign, appliedCampaigns } = require("../database");
const CampaignsPublic = express.Router();

/**
 * Helper: डेट-ओनली कम्पैरेशन करके और सीट फूल चेक करके
 * वापस देगा "Recruiting" या "Closed"
 */
function computeCampaignStatus(campaign, approvedCount) {
  // आज की date (time को zero करके)
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // deadline को भी date-only में बदलो
  let dl = null;
  if (campaign.deadline) {
    dl = new Date(campaign.deadline);
    dl.setHours(0, 0, 0, 0);
  }

  const isExpired = dl ? dl < today : false;
  const isFull =
    typeof campaign.recruiting === "number" &&
    approvedCount >= campaign.recruiting;

  // DB का status भी देखो (Active/Deactive)
  return campaign.status === "Active" && !isExpired && !isFull
    ? "Recruiting"
    : "Closed";
}

// ————————— GET /active —————————
// केवल वे campaigns जो अभी भी डेट-अनुसार ओपन हैं
CampaignsPublic.get("/active", async (req, res) => {
  try {
    // आज midnight UTC नहीं, लोकल time zero करके
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // जिनकी deadline आज या बाद की है
    const campaigns = await Campaign.find({
      deadline: { $gte: today },
    });

    const result = await Promise.all(
      campaigns.map(async (c) => {
        const approvedCount = await appliedCampaigns.countDocuments({
          campaign: c._id,
          status: "Approved",
        });

        return {
          id: c._id,
          name: c.campaignTitle,
          brand: c.brandName,
          deadline: c.deadline,
          recruitmentEndDate: c.recruitmentEndDate,
          category: c.contentFormat,
          image: c.brandLogo,
          recruiting: c.recruiting,
          applicantsCount: approvedCount,
          campaignStatus: computeCampaignStatus(c, approvedCount),
          status: c.status,
        };
      })
    );

    res.json({ status: "success", campaigns: result });
  } catch (err) {
    console.error("❌ [GET /active] Error:", err);
    res.status(500).json({ status: "failed", message: err.message });
  }
});

// ————————— GET /?page=… —————————
// Pagination + sorting + same campaignStatus logic
CampaignsPublic.get("/", async (req, res) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = 6;
  const skip = (page - 1) * limit;

  try {
    // Aggregate for hash-priority + deadline sorting
    const campaigns = await Campaign.aggregate([
      {
        $addFields: {
          hashPriority: {
            $cond: [{ $eq: [{ $substrCP: ["$brandName", 0, 1] }, "#"] }, 1, 0],
          },
        },
      },
      { $sort: { hashPriority: -1, deadline: -1, _id: 1 } },
      { $skip: skip },
      { $limit: limit },
    ]);

    if (!campaigns.length) {
      return res.json({ status: "failed", message: "No campaigns found" });
    }

    const enriched = await Promise.all(
      campaigns.map(async (c) => {
        const approvedCount = await appliedCampaigns.countDocuments({
          campaign: c._id,
          status: "Approved",
        });

        return {
          id: c._id,
          name: c.campaignTitle,
          brand: c.brandName,
          deadline: c.deadline,
          recruitmentEndDate: c.recruitmentEndDate,
          category: c.contentFormat,
          image: c.brandLogo,
          recruiting: c.recruiting,
          applicantsCount: approvedCount,
          campaignStatus: computeCampaignStatus(c, approvedCount),
          status: c.status,
          campaignType: c.campaignType,
          type: c.campaignType,
        };
      })
    );

    const total = await Campaign.countDocuments({});
    const totalPages = Math.ceil(total / limit);

    res.json({
      status: "success",
      campaigns: enriched,
      totalPages,
    });
  } catch (err) {
    console.error("❌ [GET /] Error:", err);
    res.status(500).json({ status: "failed", message: err.message });
  }
});

// ————————— GET /:id/:email —————————
// Single campaign detail + check if user applied
CampaignsPublic.get("/:id/:email", async (req, res) => {
  const { id, email } = req.params;

  try {
    const campaign = await Campaign.findById(id);
    if (!campaign) {
      return res
        .status(404)
        .json({ status: "failed", message: "Campaign not found" });
    }

    // Approved applicants का count
    const approvedCount = await appliedCampaigns.countDocuments({
      campaign: id,
      status: "Approved",
    });

    // campaignStatus फिर से कम्प्यूट करो
    const campaignStatus = computeCampaignStatus(campaign, approvedCount);

    // User ने apply किया या नहीं
    const exists = await appliedCampaigns.findOne({ email, campaign: id });

    const detail = {
      ...campaign.toObject(),
      applied: !!exists,
    };

    res.json({
      status: "success",
      campaign: detail,
      applicantsCount: approvedCount,
      campaignStatus,
    });
  } catch (err) {
    console.error("❌ [GET /:id/:email] Error:", err);
    res.status(500).json({ status: "failed", message: err.message });
  }
});

module.exports = { CampaignsPublic };
