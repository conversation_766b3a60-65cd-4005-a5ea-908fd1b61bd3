const axios = require('axios');

async function testBrandLogin() {
  try {
    const response = await axios.post('http://localhost:2340/api/brand/auth/signin', {
      email: '<EMAIL>',
      password: 'DemoUser2024!@#'
    });
    console.log('Response:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('Error response:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

testBrandLogin(); 