const mongoose = require("mongoose");
const { User, appliedCampaigns, Campaign, RequestLog } = require("../database");

async function ApplyCampaign(req, res) {
  const {
  address,
  city,
  state,
  zip,
  country, // 👈 ADD THIS
  campaignId,
  phone,
  instagramId,
  tiktokId,
} = req.body;


  const { email, name, _id, isVerified } = req.user;
  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const userAgent = req.get("User-Agent");

  try {
    // ✅ Log this action
    await RequestLog.create({
      email,
      ip,
      userAgent,
      action: "apply-campaign",
    });

    // ✅ Validate campaignId
    if (!mongoose.Types.ObjectId.isValid(campaignId)) {
      return res.status(400).json({ status: "failed", message: "Invalid campaign ID" });
    }

    // ✅ Require email verification
    if (!isVerified) {
      return res.status(403).json({
        status: "failed",
        message: "Please verify your email before applying to campaigns.",
      });
    }

    // ✅ Validate address
    if (!address || !city || !state || !country) {
  return res.status(400).json({ status: "failed", message: "Incomplete address fields." });
}

if (country === "USA") {
  if (!zip.match(/^\d{5}(-\d{4})?$/)) {
    return res.status(400).json({ status: "failed", message: "Invalid ZIP code format for USA." });
  }
} else if (country === "Canada") {
  if (!zip.match(/^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/)) {
    return res.status(400).json({ status: "failed", message: "Invalid postal code format for Canada." });
  }
} else {
  return res.status(400).json({ status: "failed", message: "Invalid country." });
}


    // ✅ Already applied check
    const alreadyApplied = await appliedCampaigns.findOne({
      email,
      campaign: campaignId,
    });

    if (alreadyApplied) {
      return res.json({
        status: "failed",
        message: "Already Applied",
      });
    }

   // ✅ Monthly limit check (count only "Completed" applications)
const now = new Date();
const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

// const completedCount = await appliedCampaigns.countDocuments({
//   email,
//   status: "Completed", // ✅ Only completed applications
//   createdAt: {
//     $gte: startOfMonth,
//     $lte: endOfMonth,
//   },
// });
// if (completedCount >= 5) {
//   return res.status(403).json({
//     status: "failed",
//     message: "You can only apply to 5 campaigns per month (counted after completion).",
//   });
// }


    const appliedThisMonth = await appliedCampaigns.countDocuments({
  email,
  createdAt: {
    $gte: startOfMonth,
    $lte: endOfMonth,
  },
});

if (appliedThisMonth >= 5) {
  return res.status(403).json({
    status: "failed",
    message: "You can only apply to 5 campaigns per month.",
  });
}


    // ✅ Confirm campaign exists
    const campaignData = await Campaign.findById(campaignId);
    if (!campaignData) {
      return res.json({ status: "failed", message: "Campaign not found" });
    }

    // ✅ Create application
    const application = new appliedCampaigns({
  name,
  email,
  address,
  country, // 👈 ADD THIS
  state,
  city,
  phone,
  zipCode: zip,
  instagramId: instagramId || "",
  tiktokId: tiktokId || "",
  campaign: campaignId,
});


    await application.save();

    // ✅ Update user social fields if needed
    const userUpdate = { $push: { appliedCampaigns: application._id } };
    const setFields = {};
    if (instagramId) setFields.instagramId = instagramId;
    if (tiktokId) setFields.tiktokId = tiktokId;
    if (Object.keys(setFields).length) {
      userUpdate.$set = setFields;
    }
    await User.findByIdAndUpdate(_id, userUpdate);

    // ✅ Final response
    return res.json({
      status: "success",
      message: "Successfully applied",
    });

  } catch (error) {
    console.error("❌ ApplyCampaign Error:", error);
    return res.status(500).json({
      status: "failed",
      message: error.message || "Something went wrong",
    });
  }
}

module.exports = {
  ApplyCampaign,
};
