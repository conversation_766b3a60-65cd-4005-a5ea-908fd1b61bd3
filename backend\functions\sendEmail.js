const nodemailer = require("nodemailer");
const dotenv = require("dotenv");
const { User, Brand } = require("../database/index");
const { FeedbackAsk_Email } = require("../emails/feedbackAsk");
const { CampaignSend } = require("../emails/broadCast");
const { default: axios } = require("axios");
dotenv.config();
const {
  SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS, SMTP_SEND_EMAIL, BREVO_SMTP_HOST, BREVO_SMTP_PORT, BREVO_SMTP_USER, BREVO_SMTP_PASS, BREVO_SEND_EMAIL, BREVO_API_KEY
} = process.env;

const frontendUrl = process.env.FRONTEND_URL?.split(",")[0].trim();

// Gmail SMTP (default for everything except welcome/reminder)
const transporter = nodemailer.createTransport({
  host: SMTP_HOST,
  port: Number(SMTP_PORT),
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASS,
  },
});

// Brevo SMTP (only for welcome/reminder)
const brevoTransporter = nodemailer.createTransport({
  host: BREVO_SMTP_HOST,
  port: Number(BREVO_SMTP_PORT),
  auth: {
    user: BREVO_SMTP_USER,
    pass: BREVO_SMTP_PASS,
  },
});

const sendWelcomeEmail = async (userEmail, name) => {
  try {
    const mailOptions = {
      from: BREVO_SEND_EMAIL, // this will be from your .env (Gmail sender)
      to: userEmail,
      subject: "Thank you for joining!",
      html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Thanks for Joining</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #000000; color: #ffffff; font-family: Arial, sans-serif;">
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" height="100%">
          <tr>
            <td align="center" valign="middle">
              <h1 style="color: #ffffff; font-size: 28px; text-align: center;">Hi ${name}, thanks for joining!</h1>
            </td>
          </tr>
        </table>
      </body>
      </html>
      `,
    };
    // ✅ Only sync contact to Brevo (via API)
    try {
      await addToBrevoContacts(userEmail, name);
    } catch (err) {
      console.error("⚠️ Brevo contact error (non-blocking):", err.message || err);
    }

    // ✅ Send email using Gmail SMTP transporter
    await brevoTransporter.sendMail(mailOptions);
    console.log("✅ Welcome email sent");
  } catch (error) {
    console.error("❌ Error sending welcome email:", error.message || error);
  }
};

const sendCampaignToAllUsers = async (campaign) => {
  try {
    const cursor = User.find({}, 'email').cursor();

    for await (const user of cursor) {
      const mailOptions = {
        from: SMTP_SEND_EMAIL,
        to: user.email,
        subject: `New Opportunity Just for You!`,
        html: CampaignSend(campaign),
      };

      try {
        await transporter.sendMail(mailOptions);
        console.log(`✅ Email sent to ${user.email}`);
      } catch (err) {
        console.error(`❌ Failed to send to ${user.email}:`, err.message || err);
      }
    }
  } catch (err) {
    console.error("❌ Error in sendCampaignToAllUsers:", err.message || err);
  }
};

const sendThanksApplyig = async (userEmail, campaign, feedbackLink) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Thanks for applying",
      html: FeedbackAsk_Email(campaign, feedbackLink),
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Thanks email sent to ${userEmail}`);
  } catch (error) {
    console.error(`❌ Error sending thanks email to ${userEmail}:`, error.message || error);
  }
};

const addToBrevoContacts = async (email, name) => {
  try {
    const listId = 6; // 🟢 Replace with your actual Brevo list ID if different

    const response = await axios.post(
      'https://api.brevo.com/v3/contacts',
      {
        email,
        attributes: { NAME: name || "" },
        updateEnabled: true,
        listIds: [listId], // 🟢 Assign to Matchably list
      },
      {
        headers: {
          'api-key': BREVO_API_KEY,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log(`✅ Contact added to Brevo: ${email}`);
  } catch (error) {
    console.error(`❌ Error adding ${email} to Brevo:`, error.response?.data || error.message || error);
  }
};

// ✅ NEW: Send Verification Email with Link
const sendVerificationLink = async (userEmail, token) => {
  try {
    const verificationUrl = `${frontendUrl}/verify-email?token=${token}`;
    
    const mailOptions = {
      from: "<EMAIL>",
      to: userEmail,
      subject: "Verify your email address - Matchably",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Verify Your Email</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
          <table align="center" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <tr>
              <td style="text-align: center;">
                <h1 style="color: #4f46e5;">Matchably</h1>
                <h2 style="color: #333;">Email Verification</h2>
                <p style="color: #555; font-size: 16px;">Hi there,</p>
                <p style="color: #555; font-size: 16px;">Please click the button below to verify your email address and complete your registration:</p>
                <a href="${verificationUrl}" target="_blank" style="display: inline-block; margin-top: 20px; padding: 12px 25px; background-color: #4f46e5; color: white; text-decoration: none; border-radius: 5px; font-size: 16px;">
                  Verify My Email
                </a>
                <p style="margin-top: 30px; color: #777;">If the button doesn't work, copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #4f46e5;">
                  <a href="${verificationUrl}" style="color: #4f46e5;">${verificationUrl}</a>
                </p>
                <p style="color: #999; margin-top: 30px;">This link will expire soon. If you didn’t request this, you can ignore this email.</p>
              </td>
            </tr>
          </table>
          <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
        </body>
        </html>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Verification email sent to ${userEmail}`);
  } catch (error) {
    const msg = error.response?.data || error.message || error;
    console.error(`❌ Failed to send verification email to ${userEmail}:`, msg);
  }
};

const sendEmailWithTempPassword = async (userEmail, tempPassword, name = "") => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Your Temporary Password - Matchably",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8" />
          <title>Temporary Password</title>
        </head>
        <body style="margin: 0; padding: 20px; background-color: #f4f4f4; font-family: Arial, sans-serif;">
          <table align="center" width="100%" style="max-width: 600px; background-color: #ffffff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <tr>
              <td style="text-align: center;">
                <h2 style="color: #4f46e5;">Hi ${name || "there"},</h2>
                <p style="font-size: 16px; color: #555;">
                  We received a request to reset your password. Please use the temporary password below to log in:
                </p>
                <p style="font-size: 20px; font-weight: bold; color: #333; margin: 20px 0;">${tempPassword}</p>
                <p style="font-size: 16px; color: #555;">
                  For your security, please log in and change your password immediately.
                </p>
                <p style="font-size: 14px; color: #888; margin-top: 30px;">
                  If you did not request this, please ignore this email.
                </p>
              </td>
            </tr>
          </table>
          <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
        </body>
        </html>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Temporary password email sent to ${userEmail}`);
  } catch (error) {
    console.error(`❌ Failed to send temporary password to ${userEmail}:`, error.message || error);
  }
};

const sendContentSubmissionReminderEmail = async (userEmail, userName, campaignTitle) => {
  try {
    const mailOptions = {
      from: BREVO_SEND_EMAIL, // From Gmail SMTP via .env
      to: userEmail,
      subject: `You're Approved for ${campaignTitle} - Submit Your Content`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8" />
          <title>Submit Your Content</title>
        </head>
        <body style="margin: 0; padding: 20px; background-color: #f4f4f4; font-family: Arial, sans-serif;">
          <table align="center" width="100%" style="max-width: 600px; background-color: #ffffff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <tr>
              <td>
                <h2 style="color: #4f46e5;">Hi ${userName},</h2>
                <p style="font-size: 16px; color: #333;">
                  Great news! Your application for the campaign <strong>${campaignTitle}</strong> has been <strong>approved</strong>.
                </p>
                <p style="font-size: 16px; color: #555;">
                  Please submit your content as soon as possible to continue your participation.
                </p>
                <p style="font-size: 16px; color: #888;">
                  Thank you for being a part of Matchably!
                </p>
              </td>
            </tr>
          </table>
          <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
        </body>
        </html>
      `,
    };

    await brevoTransporter.sendMail(mailOptions); // ✅ now using Gmail SMTP
    console.log(`✅ Reminder email sent to ${userEmail} for campaign: ${campaignTitle}`);
  } catch (error) {
    console.error(`❌ Failed to send reminder email to ${userEmail}:`, error.message || error);
  }
};

const sendRedemptionEmailToAdmin = async ({ userEmail, userName, rewardLabel, pointsUsed }) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: "<EMAIL>", // 🟡 Replace with your admin email
      subject: "🎁 New Reward Redemption",
      html: `
        <h2>🎉 New Redemption Request</h2>
        <p><strong>${userName}</strong> (${userEmail}) just redeemed a reward:</p>
        <ul>
          <li><strong>Reward:</strong> ${rewardLabel}</li>
          <li><strong>Points Used:</strong> ${pointsUsed}</li>
        </ul>
        <p>Please fulfill this reward manually.</p>
      `
    };

    await transporter.sendMail(mailOptions);
    console.log(`📨 Admin notified of reward redemption by ${userEmail}`);
  } catch (err) {
    console.error("❌ Failed to send redemption email to admin:", err.message);
  }
};

// Purchase activation email
async function sendSubscriptionActivatedEmail(brandId, planName) {
  try {
    const brand = await Brand.findById(brandId);
    if (!brand) return;
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: brand.email,
      subject: "Your Subscription is Active ✅",
      html: `
        <p>Hi ${brand.companyName},</p>
        <p>Your subscription for the <strong>${planName}</strong> plan is now active.</p>
        <p>Thank you for choosing Matchably!</p>
      `
    };
    await transporter.sendMail(mailOptions);
    console.log(`✅ Activation email sent to ${brand.email}`);
  } catch (err) {
    console.error(`❌ sendSubscriptionActivatedEmail error:`, err.message || err);
  }
}

// Upgrade confirmation email
async function sendUpgradeConfirmationEmail(brandId, newPlanName) {
  try {
    const brand = await Brand.findById(brandId);
    if (!brand) return;
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: brand.email,
      subject: "Your Plan Has Been Upgraded 🎉",
      html: `
        <p>Hi ${brand.companyName},</p>
        <p>Your plan has been upgraded to <strong>${newPlanName}</strong>.</p>
        <p>Thanks for staying with Matchably!</p>
      `
    };
    await transporter.sendMail(mailOptions);
    console.log(`✅ Upgrade email sent to ${brand.email}`);
  } catch (err) {
    console.error(`❌ sendUpgradeConfirmationEmail error:`, err.message || err);
  }
}

// Send shipment notification to creator
async function sendShipmentEmail(creatorId, campaignId, carrier, trackingNumber) {
  try {
    const user = await User.findById(creatorId);
    if (!user) return;
    const trackingUrlMap = {
      UPS: `https://www.ups.com/track?tracknum=${trackingNumber}`,
      FedEx: `https://www.fedex.com/apps/fedextrack/?tracknumbers=${trackingNumber}`,
      USPS: `https://tools.usps.com/go/TrackConfirmAction?tLabels=${trackingNumber}`
    };
    const trackingUrl = trackingUrlMap[carrier] || '';
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: user.email,
      subject: "Your product has been shipped! 🚚",
      html: `
        <p>Hi ${user.name},</p>
        <p>Your product for campaign <strong>${campaignId}</strong> has been shipped via <strong>${carrier}</strong>.</p>
        <p>Your tracking number is <strong>${trackingNumber}</strong>.</p>
        ${trackingUrl ? `<p>Track your shipment <a href="${trackingUrl}" target="_blank">here</a>.</p>` : ''}
        <p>Thank you for participating!</p>
      `
    };
    await transporter.sendMail(mailOptions);
    console.log(`✅ Shipment email sent to ${user.email}`);
  } catch (err) {
    console.error(`❌ sendShipmentEmail error:`, err.message || err);
  }
}

// Send email to brand upon approval
async function sendBrandApprovalEmail(brandEmail, companyName) {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: brandEmail,
      subject: "Your Brand Account is Approved ✅",
      html: `
        <p>Hi ${companyName},</p>
        <p>Your brand account has been approved! You can now log in and launch your first campaign.</p>
        <p>Welcome aboard!</p>
      `
    };
    await transporter.sendMail(mailOptions);
    console.log(`✅ Brand approval email sent to ${brandEmail}`);
  } catch (err) {
    console.error(`❌ sendBrandApprovalEmail error:`, err.message || err);
  }
}

// Send email to brand upon rejection
async function sendBrandRejectionEmail(brandEmail, companyName) {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: brandEmail,
      subject: "Brand Sign-Up Not Approved ⚠️",
      html: `
        <p>Hi ${companyName},</p>
        <p>Unfortunately, we could not approve your sign-up request at this time.</p>
        <p>If you believe this was a mistake, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
      `
    };
    await transporter.sendMail(mailOptions);
    console.log(`✅ Brand rejection email sent to ${brandEmail}`);
  } catch (err) {
    console.error(`❌ sendBrandRejectionEmail error:`, err.message || err);
  }
}

// Send email to brand upon BrandSignupEmail
async function sendBrandSignupEmail(brandEmail, companyName) {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: brandEmail,
      subject: "Brand Signup Received 📝",
      html: `
        <p>Hi ${companyName},</p>
        <p>Thank you for signing up! Your application is under review. We will notify you once it's approved.</p>
        <p>Best,<br/>The Matchably Team</p>
      `
    };
    await transporter.sendMail(mailOptions);
    console.log(`✅ Signup email sent to ${brandEmail}`);
  } catch (err) {
    console.error(`❌ sendBrandSignupEmail error:`, err.message || err);
  }
}

/**
 * Send approval notification to brand
 */
async function sendBrandApprovalEmail(email, companyName) {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: email,
      subject: 'Your Brand Account is Approved ✅',
      html: `
        <p>Hi ${companyName},</p>
        <p>Great news! Your brand account has just been approved. 🎉</p>
        <p>You can now log in and start creating campaigns.</p>
        <p>Best regards,<br/>Matchably Team</p>
      `,
    };
    await transporter.sendMail(mailOptions);
    console.log(`✅ Approval email sent to ${email}`);
  } catch (err) {
    console.error(`❌ Error sending approval email to ${email}:`, err);
  }
}

/**
 * Send rejection notification to brand
 */
async function sendBrandRejectionEmail(email, companyName) {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: email,
      subject: 'Brand Sign-Up Not Approved ⚠️',
      html: `
        <p>Hi ${companyName},</p>
        <p>We’re sorry to inform you that your brand sign-up request was not approved at this time.</p>
        <p>If you have questions, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
        <p>Thank you for your interest.</p>
      `,
    };
    await transporter.sendMail(mailOptions);
    console.log(`✅ Rejection email sent to ${email}`);
  } catch (err) {
    console.error(`❌ Error sending rejection email to ${email}:`, err);
  }
}

/**
 * Send shipment tracking email to creator
 */
async function sendTrackingEmailToUser(email, trackingLink) {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: email,
      subject: '📦 Your Shipment is on the Way!',
      html: `
        <p>Hi there,</p>
        <p>Your shipment tracking link has been updated.</p>
        <p>You can track your package here:</p>
        <p><a href="${trackingLink}" target="_blank">${trackingLink}</a></p>
        <p>If you have any questions, feel free to reach out.</p>
        <br/>
        <p>– Matchably Team</p>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Tracking email sent to ${email}`);
  } catch (err) {
    console.error(`❌ Error sending tracking email to ${email}:`, err);
  }
}

/**
 * Send temporary password email to brand
 */
async function sendBrandTempPassword(email, tempPassword, companyName) {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: email,
      subject: '🔐 Matchably Brand - Temporary Password',
      html: `
        <p>Hello <strong>${companyName}</strong>,</p>
        <p>You've requested a password reset for your Matchably brand account.</p>
        <p>Your temporary password is:</p>
        <h3>${tempPassword}</h3>
        <p>Please use this password to log in and change it immediately for security reasons.</p>
        <br/>
        <p>If you did not request this, please contact our support team.</p>
        <br/>
        <p>– Matchably Team</p>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Temporary password sent to ${email}`);
  } catch (err) {
    console.error(`❌ Error sending temp password to brand ${email}:`, err);
  }
}




module.exports = { 
  sendWelcomeEmail, 
  sendCampaignToAllUsers,
  sendThanksApplyig, 
  sendVerificationLink, 
  sendEmailWithTempPassword, 
  sendContentSubmissionReminderEmail, 
  sendRedemptionEmailToAdmin, 
  sendSubscriptionActivatedEmail,
  sendUpgradeConfirmationEmail, 
  sendShipmentEmail, 
  sendBrandApprovalEmail,
  sendBrandRejectionEmail,
  sendBrandSignupEmail,
  sendBrandApprovalEmail,
  sendBrandRejectionEmail,
  sendTrackingEmailToUser,
  sendBrandTempPassword,
};
