// routes/brand/tracking.js
const express = require('express');
const router = express.Router();

// Controllers
const {
  addTrackingInfo,
  getTrackingForCreator,
  updateTrackingInfo,
  getTrackingHistory
} = require('../../middlewares/brand/trackingController');
const { verifyBrandToken } = require('../../middlewares/brand/authController');

// Brand adds tracking info
router.post('/add', verifyBrandToken, addTrackingInfo);

// Creator views their tracking info
router.get('/creator/:campaignId', verifyBrandToken, getTrackingForCreator);

// Brand updates tracking info
router.post('/update/:trackingId', verifyBrandToken, updateTrackingInfo);

// Get detailed tracking history
router.get('/history/:trackingId', verifyBrandToken, getTrackingHistory);

module.exports = router;
