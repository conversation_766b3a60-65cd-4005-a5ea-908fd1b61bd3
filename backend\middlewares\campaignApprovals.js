const { BrandCampaignRequest, BrandSubscription, Campaign } = require('../database/index');

/**
 * Get all pending brand campaign requests (Admin side)
 */
async function getPendingRequests(req, res) {
  try {
    const requests = await BrandCampaignRequest
      .find({ approvalStatus: 'Pending' })
      .populate('brand');

    return res.json({ status: 'success', requests });
  } catch (err) {
    console.error('❌ getPendingRequests error:', err);
    return res.status(500).json({ status: 'failed', message: 'Error fetching requests' });
  }
}

/**
 * Approve a pending campaign request and create a live Campaign (Admin side)
 */
async function approveCampaignRequest(req, res) {
  try {
    const { id } = req.params;
    const request = await BrandCampaignRequest.findById(id);

    if (!request || request.approvalStatus !== 'Pending') {
      return res.status(400).json({ status: 'failed', message: 'Invalid or already processed request' });
    }

    // Fetch active subscription and include any extra campaigns
    const subscription = await BrandSubscription
      .findOne({ brand: request.brand, status: 'active' })
      .populate('plan');

    if (!subscription) {
      return res.status(404).json({ status: 'failed', message: 'Active subscription not found' });
    }

    // // Calculate total allowed campaigns including extras
    // const baseAllowed = subscription.plan.campaignsAllowed || 0;
    // const extras = subscription.extraCampaignsAllowed || 0;
    // const totalAllowed = baseAllowed + extras;

    // // Check limit
    // if (subscription.campaignsUsed >= totalAllowed) {
    //   return res.status(403).json({
    //     status: 'failed',
    //     message: `Campaign limit reached (used ${subscription.campaignsUsed} of ${totalAllowed})`
    //   });
    // }

    // Create new live campaign using request fields
    const newCampaign = await Campaign.create({
      campaignTitle:             request.campaignTitle,
      campaignIndustry:          request.campaignIndustry,
      campaignType:              request.campaignType,
      category:                  request.category,
      brandName:                 request.brandName,
      productName:               request.productName,
      productDescription:        request.productDescription,
      brandLogo:                 request.brandLogo,
      productImages:             request.productImages,
      creatorCount:              request.creatorCount,
      recruiting:                request.creatorCount, // ✅ Copy creatorCount value
      influencersReceive:        request.influencersReceive,
      contentFormat:             request.contentFormat,
      requiredHashtags:          request.requiredHashtags,
      mentionHandle:             request.mentionHandle,
      toneGuide:                 request.toneGuide,
      referenceContent:          request.referenceContent,
      participationRequirements: request.participationRequirements,
      deadline:                  request.deadline,
      recruitmentEndDate:        request.recruitmentEndDate,
      beautyDetails:             request.beautyDetails,
      foodDetails:               request.foodDetails,
      beverageDetails:           request.beverageDetails,
      wellnessDetails:           request.wellnessDetails,
      personalCareDetails:       request.personalCareDetails,
      status:                    'Active',
      referenceId:               request._id,
      // Paid campaign fields (only if paid)
      ...(request.campaignType === 'paid' ? {
        pricingModel: request.pricingModel,
        fixedPrice: request.fixedPrice,
        minBid: request.minBid,
        maxBid: request.maxBid,
        requestedContent: request.requestedContent,
        creatorRequirements: request.creatorRequirements,
        usageTerms: request.usageTerms,
      } : {}),
    });


    // // Increment subscription usage and save
    // subscription.campaignsUsed += 1;
    // await subscription.save();

    // Mark request as approved and save
    request.approvalStatus = 'Approved';
    await request.save();

    return res.json({ status: 'success', campaign: newCampaign });
  } catch (err) {
    console.error('❌ approveCampaignRequest error:', err);
    return res.status(500).json({ status: 'failed', message: 'Error approving campaign' });
  }
}

/**
 * Reject a pending campaign request (Admin side)
 */
async function rejectCampaignRequest(req, res) {
  try {
    const { id } = req.params;
    const request = await BrandCampaignRequest.findById(id);

    if (!request) {
      return res.status(404).json({ status: 'failed', message: 'Request not found' });
    }

    // Mark as rejected and save
    request.approvalStatus = 'Rejected';
    await request.save();

    return res.json({ status: 'success', message: 'Campaign request rejected' });
  } catch (err) {
    console.error('❌ rejectCampaignRequest error:', err);
    return res.status(500).json({ status: 'failed', message: 'Error rejecting campaign' });
  }
}

module.exports = {
  getPendingRequests,
  approveCampaignRequest,
  rejectCampaignRequest
};
