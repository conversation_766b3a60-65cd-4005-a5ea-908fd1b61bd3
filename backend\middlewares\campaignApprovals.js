const { BrandCampaignRequest, BrandSubscription, Campaign } = require('../database/index');
const { sendPaidAccessApprovalEmail, sendPaidAccessRejectionEmail } = require('../functions/sendEmail');

/**
 * Get all pending brand campaign requests (Admin side)
 */
async function getPendingRequests(req, res) {
  try {
    const requests = await BrandCampaignRequest
      .find({ approvalStatus: 'Pending' })
      .populate('brand');

    return res.json({ status: 'success', requests });
  } catch (err) {
    console.error('❌ getPendingRequests error:', err);
    return res.status(500).json({ status: 'failed', message: 'Error fetching requests' });
  }
}

/**
 * Approve a pending campaign request and create a live Campaign (Admin side)
 */
async function approveCampaignRequest(req, res) {
  try {
    const { id } = req.params;
    const request = await BrandCampaignRequest.findById(id).populate('brand');

    if (!request || request.approvalStatus !== 'Pending') {
      return res.status(400).json({ status: 'failed', message: 'Invalid or already processed request' });
    }

    // Fetch active subscription and include any extra campaigns
    const subscription = await BrandSubscription
      .findOne({ brand: request.brand._id || request.brand, status: 'active' })
      .populate('plan');

    if (!subscription) {
      return res.status(404).json({ status: 'failed', message: 'Active subscription not found' });
    }

    // Create new live campaign using request fields
    const newCampaign = await Campaign.create({
      campaignTitle:             request.campaignTitle,
      campaignIndustry:          request.campaignIndustry,
      campaignType:              request.campaignType,
      category:                  request.category,
      brandName:                 request.brandName,
      productName:               request.productName,
      productDescription:        request.productDescription,
      brandLogo:                 request.brandLogo,
      productImages:             request.productImages,
      creatorCount:              request.creatorCount,
      recruiting:                request.creatorCount, // ✅ Copy creatorCount value
      influencersReceive:        request.influencersReceive,
      contentFormat:             request.contentFormat,
      requiredHashtags:          request.requiredHashtags,
      mentionHandle:             request.mentionHandle,
      toneGuide:                 request.toneGuide,
      referenceContent:          request.referenceContent,
      participationRequirements: request.participationRequirements,
      deadline:                  request.deadline,
      recruitmentEndDate:        request.recruitmentEndDate,
      beautyDetails:             request.beautyDetails,
      foodDetails:               request.foodDetails,
      beverageDetails:           request.beverageDetails,
      wellnessDetails:           request.wellnessDetails,
      personalCareDetails:       request.personalCareDetails,
      status:                    'Active',
      referenceId:               request._id,
      // Paid campaign fields (only if paid)
      ...(request.campaignType === 'paid' ? {
        pricingModel: request.pricingModel,
        fixedPrice: request.fixedPrice,
        minBid: request.minBid,
        maxBid: request.maxBid,
        requestedContent: request.requestedContent,
        creatorRequirements: request.creatorRequirements,
        usageTerms: request.usageTerms,
      } : {}),
    });

    // Mark request as approved and save
    request.approvalStatus = 'Approved';
    await request.save();

    // Send notification email if paid campaign
    if (request.campaignType === 'paid' && request.brand && request.brand.email && request.brand.companyName) {
      await sendPaidAccessApprovalEmail(request.brand.email, request.brand.companyName);
    }

    return res.json({ status: 'success', campaign: newCampaign });
  } catch (err) {
    console.error('❌ approveCampaignRequest error:', err);
    return res.status(500).json({ status: 'failed', message: 'Error approving campaign' });
  }
}

/**
 * Reject a pending campaign request (Admin side)
 */
async function rejectCampaignRequest(req, res) {
  try {
    const { id } = req.params;
    const { rejection_reason } = req.body;
    const request = await BrandCampaignRequest.findById(id).populate('brand');

    if (!request) {
      return res.status(404).json({ status: 'failed', message: 'Request not found' });
    }

    // Mark as rejected and save
    request.approvalStatus = 'Rejected';
    if (typeof rejection_reason === 'string') {
      request.rejection_reason = rejection_reason.slice(0, 200); // enforce max length
    }
    await request.save();

    // Send notification email if paid campaign
    if (request.campaignType === 'paid' && request.brand && request.brand.email && request.brand.companyName) {
      await sendPaidAccessRejectionEmail(request.brand.email, request.brand.companyName, rejection_reason || '');
    }

    return res.json({ status: 'success', message: 'Campaign request rejected' });
  } catch (err) {
    console.error('❌ rejectCampaignRequest error:', err);
    return res.status(500).json({ status: 'failed', message: 'Error rejecting campaign' });
  }
}

module.exports = {
  getPendingRequests,
  approveCampaignRequest,
  rejectCampaignRequest
};
