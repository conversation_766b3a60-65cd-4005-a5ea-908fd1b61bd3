const { BrandSubscription }      = require("../../database/index");
const { createCheckoutSession }   = require("../../utils/stripeUtils");


/**
 * POST /brand/package/subscribe
 * Initiate a new plan purchase via Stripe Checkout
 */
async function purchasePlan(req, res) {
  try {
    const { planId } = req.body;
    if (!planId) {
      return res.status(400).json({ status: "failed", message: "planId is required" });
    }

    // Create Stripe Checkout session for purchase
    const session = await createCheckoutSession({
      brandId:   req.user.id,
      planId,
      isUpgrade: false
    });

    return res.json({ status: "success", url: session.url });
  } catch (error) {
    console.error("❌ purchasePlan error:", error);
    return res.status(400).json({ status: "failed", message: error.message });
  }
}

/**
 * POST /brand/package/upgrade
 * Initiate an upgrade within the 7-day window via Stripe Checkout
 */
async function upgradePlan(req, res) {
  try {
    const { newPlanId } = req.body;
    if (!newPlanId) {
      return res.status(400).json({ status: "failed", message: "newPlanId is required" });
    }

    // Fetch active subscription
    const sub = await BrandSubscription.findOne({ brand: req.user.id, status: "active" });
    if (!sub) {
      return res.status(404).json({ status: "failed", message: "No active subscription found" });
    }

    // Enforce upgrade window
    const now = new Date();
    if (now > new Date(sub.upgradeEligibleTill)) {
      return res.status(403).json({ status: "failed", message: "Upgrade window has expired" });
    }
    // Create Stripe Checkout session for upgrade
    const session = await createCheckoutSession({
      brandId:          req.user.id,
      planId:           newPlanId,
      isUpgrade:        true,
      upgradeFromPlanId: sub.plan.toString()
    });

    return res.json({ status: "success", url: session.url });

  } catch (error) {
    console.error("❌ upgradePlan error:", error);
    return res.status(400).json({ status: "failed", message: error.message });
  }
}

/**
 * POST /brand/package/addon
 * Initiate an add-on purchase via Stripe Checkout (extra campaigns or creators)
 */
async function purchaseAddon(req, res) {
  try {
    const { addonType, quantity } = req.body;
    // Validate addonType and quantity
    if (!["campaign", "creator"].includes(addonType) || !Number.isInteger(quantity) || quantity <= 0) {
      return res.status(400).json({ status: "failed", message: "Invalid addonType or quantity" });
    }

    // Fetch active subscription with plan details
    const sub = await BrandSubscription.findOne({ brand: req.user.id, status: "active" }).populate("plan");
    if (!sub) {
      return res.status(404).json({ status: "failed", message: "No active subscription found" });
    }

    // Business rule example: require 80% usage of base campaigns before extra
    if (addonType === "campaign") {
      const baseAllowed = sub.plan.campaignsAllowed;
      const used        = sub.campaignsUsed;
      if (used < 0.8 * baseAllowed) {
        return res.status(403).json({
          status:  "failed",
          message: "You must use at least 80% of your base campaigns before purchasing extras"
        });
      }
    }
    // Create Stripe Checkout session for addon
    const session = await createCheckoutSession({
      brandId:        req.user.id,
      planId:         sub.plan._id.toString(),
      isAddon:        true,
      subscriptionId: sub._id.toString(),
      addonType,
      quantity:       quantity.toString()
    });

    return res.json({ status: "success", url: session.url });

  } catch (error) {
    console.error("❌ purchaseAddon error:", error);
    return res.status(400).json({ status: "failed", message: error.message });
  }
}

/**
 * GET /brand/package/subscription
 * Retrieve the current active subscription for the brand
 */
async function getSubscription(req, res) {
  try {
    const sub = await BrandSubscription
      .findOne({ brand: req.user.id, status: "active" })
      .populate("plan");

    if (!sub) {
      return res.status(404).json({ status: "failed", message: "No active subscription" });
    }

    return res.json({ status: "success", subscription: sub });
  } catch (error) {
    console.error("❌ getSubscription error:", error);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
}

module.exports = {
  purchasePlan,
  upgradePlan,
  purchaseAddon,
  getSubscription
};
