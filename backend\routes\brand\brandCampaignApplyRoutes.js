const express = require('express');
const router = express.Router();
const { getApplications, updateApplicationStatus, deleteApplication } = require('../../middlewares/brand/brandCampaignApplyController');
const { verifyBrandToken } = require('../../middlewares/brand/authController');

router.get('/campaign-apply/:id', verifyBrandToken, getApplications);
router.patch('/campaign-apply/:applicationId/status', verifyBrandToken, updateApplicationStatus);
router.delete('/campaign-apply/:campaignId/:applicantId', verifyBrandToken, deleteApplication);

module.exports = router;