{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.3", "@heroicons/react": "^2.2.0", "@mui/material": "^7.0.1", "@react-oauth/google": "^0.12.1", "@tailwindcss/vite": "^4.0.17", "aos": "^2.3.4", "axios": "^1.9.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "framer-motion": "^12.19.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "keen-slider": "^6.8.6", "lucide-react": "^0.486.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.4.1", "react-toastify": "^11.0.5", "swiper": "^11.2.6", "tailwindcss": "^4.0.17", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}