/** @format */

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { LoaderCircle, ArrowLeft } from 'lucide-react';
import axios from 'axios';
import config from '../config';

const Campaign = () => {
  const { campaignId } = useParams();
  const navigate = useNavigate();
  const [campaign, setCampaign] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showProductInfo, setShowProductInfo] = useState(false);
  const [showPricing, setShowPricing] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  // Category color mapping for badges
  const getCategoryBadgeColor = (category) => {
    const colorMap = {
      'Beauty': 'bg-pink-500 text-white shadow-lg shadow-pink-500/30',
      'Food': 'bg-orange-500 text-white shadow-lg shadow-orange-500/30',
      'Beverage': 'bg-blue-500 text-white shadow-lg shadow-blue-500/30',
      'Wellness & Supplements': 'bg-green-500 text-white shadow-lg shadow-green-500/30',
      'Personal Care': 'bg-purple-500 text-white shadow-lg shadow-purple-500/30',
      'K-food': 'bg-red-500 text-white shadow-lg shadow-red-500/30',
      'default': 'bg-gray-500 text-white shadow-lg shadow-gray-500/30'
    };
    return colorMap[category] || colorMap['default'];
  };

  // Keyboard navigation for lightbox
  const handleLightboxKey = useCallback((e) => {
    if (!lightboxOpen) return;
    if (e.key === 'Escape') setLightboxOpen(false);
    if (e.key === 'ArrowRight') setLightboxIndex((prev) => (campaign.productImages && campaign.productImages.length > 1 ? (prev + 1) % campaign.productImages.length : prev));
    if (e.key === 'ArrowLeft') setLightboxIndex((prev) => (campaign.productImages && campaign.productImages.length > 1 ? (prev - 1 + campaign.productImages.length) % campaign.productImages.length : prev));
  }, [lightboxOpen, campaign?.productImages]);

  useEffect(() => {
    if (lightboxOpen) {
      document.body.style.overflow = 'hidden';
      window.addEventListener('keydown', handleLightboxKey);
    } else {
      document.body.style.overflow = '';
      window.removeEventListener('keydown', handleLightboxKey);
    }
    return () => {
      document.body.style.overflow = '';
      window.removeEventListener('keydown', handleLightboxKey);
    };
  }, [lightboxOpen, handleLightboxKey]);

  useEffect(() => {
    if (campaignId) {
      fetchCampaign();
    }
  }, [campaignId]);

  const fetchCampaign = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${config.BACKEND_URL}/api/user/campaigns/${campaignId}`);
      
      if (response.data.status === 'success') {
        setCampaign(response.data.campaign);
      } else {
        console.error('Failed to fetch campaign:', response.data.message);
      }
    } catch (error) {
      console.error('Error fetching campaign:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApplyNow = () => {
    // Implement the logic to handle applying for the campaign
    console.log('Applying for the campaign');
    setShowSuccessModal(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <LoaderCircle className="animate-spin text-lime-400" size={48} />
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Campaign Not Found</h2>
          <button 
            onClick={() => navigate('/campaigns')}
            className="bg-lime-500 hover:bg-lime-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Back to Campaigns
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <Helmet>
        <title>{campaign.campaignTitle} - {campaign.brandName} | Matchably</title>
        <meta name="description" content={campaign.productDescription} />
      </Helmet>

      {/* Header Section */}
      <div className="bg-gradient-to-r from-[#272727] via-[#101010] to-[#262626] border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Back Button */}
          <button 
            onClick={() => navigate('/campaigns')}
            className="flex items-center gap-2 text-gray-400 hover:text-white mb-6 transition-colors"
          >
            <ArrowLeft size={20} />
            <span>Back to Campaigns</span>
          </button>

          {/* 1️⃣ Campaign Overview (Always Open) */}
          <div className="bg-[#1a1a1a] rounded-2xl p-6 border border-gray-700">
            <h2 className="text-xl font-bold text-[#E0FFFA] mb-4 FontNoto">1️⃣ Campaign Overview</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Left Column - Basic Info */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Brand:</span>
                  <span className="text-white">{campaign.brandName || 'MIMUMIMU'}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Campaign:</span>
                  <span className="text-white">{campaign.campaignTitle || 'Summer Glow'}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Product:</span>
                  <span className="text-white">{campaign.productName || 'Niacindy Facial Glow Ampoule'}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Category:</span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${getCategoryBadgeColor(campaign.campaignIndustry)}`}>
                    {campaign.campaignIndustry || 'Beauty'}
                  </span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Platform:</span>
                  <span className="text-white">{campaign.category?.join(', ') || 'TikTok'}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Deadline:</span>
                  <span className="text-white">
                    {campaign.deadline ? new Date(campaign.deadline).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) : 'July 10, 2025'}
                  </span>
                </div>
              </div>
              
              {/* Right Column - Rewards & Requirements */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Rewards:</span>
                  <div className="flex items-center gap-2">
                    <span className="text-green-400">🎁 Free Product</span>
                    {campaign.influencersReceive && campaign.influencersReceive.toLowerCase().includes('paid') && (
                      <span className="text-yellow-400">| 💵 Paid: $50</span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Content:</span>
                  <span className="text-white">📌 1 Video Required</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Spots:</span>
                  <span className="text-white">{campaign.recruiting ? `${campaign.recruiting} creators` : 'Unlimited'}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-[#7EFCD8] font-medium w-20">Status:</span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${
                    campaign.status === 'Active' || campaign.status === 'active' 
                      ? 'bg-green-600 text-white' 
                      : 'bg-red-600 text-white'
                  }`}>
                    {campaign.status || 'Active'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 2️⃣ Content Guidelines (Always Open) */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-[#1a1a1a] rounded-2xl p-6 border border-gray-700">
          <h2 className="text-xl font-bold text-[#E0FFFA] mb-4 FontNoto">2️⃣ Content Guidelines</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Guidelines */}
            <div className="space-y-4">
              {/* Required Hashtags */}
              <div>
                <h3 className="text-[#7EFCD8] font-medium mb-2">Required Hashtags:</h3>
                <div className="flex flex-wrap items-center gap-x-2 gap-y-1 mb-3 text-base font-medium">
                  {(campaign.requiredHashtags?.length ? campaign.requiredHashtags : ['#niacindy', '#kbeauty', '#glowampoule', '#mimumimu']).map((hashtag, idx) => (
                    <span key={idx} className="text-lime-400">{hashtag}</span>
                  ))}
                  <span className="text-blue-400 ml-2">@matchably.official</span>
                  <button
                    onClick={() => {
                      const hashtags = (campaign.requiredHashtags?.join(' ') || '#niacindy #kbeauty #glowampoule #mimumimu') + ' @matchably.official';
                      navigator.clipboard.writeText(hashtags);
                    }}
                    className="ml-auto bg-transparent text-lime-400 border border-lime-400 px-2 py-0.5 rounded text-xs hover:bg-lime-500 hover:text-white transition-colors"
                    style={{marginLeft: 'auto'}}
                    aria-label="Copy hashtags and mention"
                  >
                    📋 Copy
                  </button>
                </div>
              </div>

              {/* @Mention */}
              <div>
                <h3 className="text-[#7EFCD8] font-medium mb-2">@Mention:</h3>
                <div className="flex items-center gap-3">
                  <span className="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-lg text-sm font-medium border border-blue-500/30">
                    @matchably.official
                  </span>
                  <button 
                    onClick={() => navigator.clipboard.writeText('@matchably.official')}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors"
                  >
                    Copy
                  </button>
                </div>
              </div>

              {/* Tone */}
              <div>
                <h3 className="text-[#7EFCD8] font-medium mb-2">Tone:</h3>
                <p className="text-gray-300 text-sm">
                  Bright, positive, emphasize before/after transformation
                </p>
              </div>

              {/* Avoid */}
              <div>
                <h3 className="text-[#7EFCD8] font-medium mb-2">Avoid:</h3>
                <p className="text-red-400 text-sm">
                  "Good for sensitive skin"
                </p>
              </div>
            </div>

            {/* Right Column - Reference Content */}
            <div className="space-y-4">
              <h3 className="text-[#7EFCD8] font-medium mb-3">🎞️ Reference Content:</h3>
              
              {/* Reference Images */}
              {campaign.productImages && campaign.productImages.length > 0 ? (
                <div className="grid grid-cols-2 gap-3">
                  {campaign.productImages.slice(0, 4).map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={image}
                        alt={`Reference ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg border border-gray-600 group-hover:border-lime-400 transition-colors cursor-pointer"
                        onClick={() => { setLightboxIndex(index); setLightboxOpen(true); }}
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <span className="text-white text-xs font-medium">Click to enlarge</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-[#262626] rounded-lg p-4 border border-gray-600">
                  <p className="text-gray-400 text-sm text-center">
                    Reference images will be provided after campaign approval
                  </p>
                </div>
              )}

              {/* External Links */}
              <div className="bg-[#262626] rounded-lg p-4 border border-gray-600">
                <h4 className="text-[#7EFCD8] font-medium mb-2 text-sm">Additional Resources:</h4>
                <div className="space-y-2">
                  <a 
                    href="#" 
                    className="block text-blue-400 hover:text-blue-300 text-sm underline"
                  >
                    📖 Brand Guidelines PDF
                  </a>
                  <a 
                    href="#" 
                    className="block text-blue-400 hover:text-blue-300 text-sm underline"
                  >
                    🎥 Content Examples
                  </a>
                  <a 
                    href="#" 
                    className="block text-blue-400 hover:text-blue-300 text-sm underline"
                  >
                    📱 Platform Best Practices
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 3️⃣ Product Info & Images (Collapsed by Default) */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-[#1a1a1a] rounded-2xl border border-gray-700 overflow-hidden">
          {/* Collapsible Header */}
          <button 
            onClick={() => setShowProductInfo(!showProductInfo)}
            className="w-full p-6 text-left flex items-center justify-between hover:bg-[#262626] transition-colors"
          >
            <h2 className="text-xl font-bold text-[#E0FFFA] FontNoto">3️⃣ Product Info & Images</h2>
            <span className="text-[#7EFCD8] text-2xl transition-transform duration-200">
              {showProductInfo ? '▼' : '▶'}
            </span>
          </button>

          {/* Collapsible Content */}
          {showProductInfo && (
            <div className="px-6 pb-6 border-t border-gray-700">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 pt-6">
                {/* Left Column - Product Info */}
                <div className="space-y-4">
                  {/* Highlights */}
                  <div>
                    <h3 className="text-[#7EFCD8] font-medium mb-2">Highlights:</h3>
                    <div className="space-y-2">
                      <div className="flex items-start">
                        <span className="text-lime-400 mr-2">•</span>
                        <span className="text-gray-300 text-sm">Targets dark spots</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-lime-400 mr-2">•</span>
                        <span className="text-gray-300 text-sm">Niacinamide + Thioctic Acid</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-lime-400 mr-2">•</span>
                        <span className="text-gray-300 text-sm">Suitable for all skin types</span>
                      </div>
                    </div>
                  </div>

                  {/* How to Use */}
                  <div>
                    <h3 className="text-[#7EFCD8] font-medium mb-2">How to Use:</h3>
                    <div className="space-y-2">
                      <div className="flex items-start">
                        <span className="text-lime-400 mr-2">1.</span>
                        <span className="text-gray-300 text-sm">Apply after toner, AM & PM</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-lime-400 mr-2">2.</span>
                        <span className="text-gray-300 text-sm">Follow with moisturizer and SPF (AM)</span>
                      </div>
                    </div>
                  </div>

                  {/* Product Details */}
                  <div>
                    <h3 className="text-[#7EFCD8] font-medium mb-2">Product Details:</h3>
                    <div className="bg-[#262626] rounded-lg p-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400 text-sm">Texture:</span>
                        <span className="text-white text-sm">Roll-on Ampoule</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400 text-sm">Size:</span>
                        <span className="text-white text-sm">30ml</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400 text-sm">Usage:</span>
                        <span className="text-white text-sm">Morning + Night</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Column - Images */}
                <div>
                  <h3 className="text-[#7EFCD8] font-medium mb-3">Images:</h3>
                  
                  {/* Main Image */}
                  <div className="mb-4">
                    <img
                      src={campaign.productImages?.[0] || campaign.productImage || 'https://via.placeholder.com/400/300/84cc16/ffffff?text=Product+Image'}
                      alt={campaign.productName || 'Product'}
                      className="w-full h-48 object-cover rounded-lg border border-gray-600 hover:border-lime-400 transition-colors cursor-pointer"
                      onClick={() => setSelectedImageIndex(0)}
                    />
                    <p className="text-gray-400 text-xs mt-1 text-center">[Main image] - Click to enlarge</p>
                  </div>

                  {/* Thumbnail Images */}
                  {campaign.productImages && campaign.productImages.length > 1 ? (
                    <div>
                      <p className="text-gray-400 text-xs mb-2">[Thumbnail 2-4] - Click to enlarge</p>
                      <div className="grid grid-cols-3 gap-2">
                        {campaign.productImages.slice(1, 4).map((image, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={image}
                              alt={`Product view ${index + 2}`}
                              className="w-full h-20 object-cover rounded-lg border border-gray-600 group-hover:border-lime-400 transition-colors cursor-pointer"
                              onClick={() => { setLightboxIndex(index + 1); setLightboxOpen(true); }}
                            />
                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                              <span className="text-white text-xs font-medium">Enlarge</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="bg-[#262626] rounded-lg p-4 border border-gray-600">
                      <p className="text-gray-400 text-sm text-center">
                        Additional product images will be available after campaign approval
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 4️⃣ Reward / Pricing (Paid Campaigns Only) (Collapsed by Default) */}
      {campaign.influencersReceive && campaign.influencersReceive.toLowerCase().includes('paid') && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-[#1a1a1a] rounded-2xl border border-gray-700 overflow-hidden">
            {/* Collapsible Header */}
            <button 
              onClick={() => setShowPricing(!showPricing)}
              className="w-full p-6 text-left flex items-center justify-between hover:bg-[#262626] transition-colors"
            >
              <h2 className="text-xl font-bold text-[#E0FFFA] FontNoto">4️⃣ Reward & Deliverables</h2>
              <span className="text-[#7EFCD8] text-2xl transition-transform duration-200">
                {showPricing ? '▼' : '▶'}
              </span>
            </button>

            {/* Collapsible Content */}
            {showPricing && (
              <div className="px-6 pb-6 border-t border-gray-700">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 pt-6">
                  {/* Left Column - Pricing Model */}
                  <div className="space-y-4">
                    <h3 className="text-[#7EFCD8] font-medium mb-3">💰 Pricing Model:</h3>
                    
                    {/* Fixed Price Option */}
                    <div className="bg-[#262626] rounded-lg p-4 border border-gray-600">
                      <div className="flex items-center mb-3">
                        <input 
                          type="radio" 
                          id="fixed-price" 
                          name="pricing-model" 
                          className="mr-3 text-lime-500 focus:ring-lime-500"
                          defaultChecked
                        />
                        <label htmlFor="fixed-price" className="text-white font-medium">Fixed Price: $50</label>
                      </div>
                      <p className="text-gray-400 text-sm">Set compensation amount for all creators</p>
                    </div>

                    {/* Bidding Option */}
                    <div className="bg-[#262626] rounded-lg p-4 border border-gray-600">
                      <div className="flex items-center mb-3">
                        <input 
                          type="radio" 
                          id="bidding" 
                          name="pricing-model" 
                          className="mr-3 text-lime-500 focus:ring-lime-500"
                        />
                        <label htmlFor="bidding" className="text-white font-medium">Bidding: Min $30 – Max $80</label>
                      </div>
                      <p className="text-gray-400 text-sm">Creators can bid within the specified range</p>
                    </div>

                    {/* Payment Terms */}
                    <div className="bg-[#1a1a1a] rounded-lg p-4 border border-gray-600">
                      <h4 className="text-[#7EFCD8] font-medium mb-2 text-sm">Payment Terms:</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Payment Method:</span>
                          <span className="text-white">PayPal / Bank Transfer</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Payment Timeline:</span>
                          <span className="text-white">Within 7 days of approval</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Tax Responsibility:</span>
                          <span className="text-white">Creator's responsibility</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Requested Content */}
                  <div className="space-y-4">
                    <h3 className="text-[#7EFCD8] font-medium mb-3">📷 Requested Content:</h3>
                    
                    {/* Content Types */}
                    <div className="space-y-3">
                      {/* TikTok Videos */}
                      <div className="bg-[#262626] rounded-lg p-4 border border-gray-600">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <span className="text-2xl mr-3">📱</span>
                            <div>
                              <h4 className="text-white font-medium">TikTok Videos</h4>
                              <p className="text-gray-400 text-sm">Vertical format, 15-60 seconds</p>
                            </div>
                          </div>
                          <span className="text-lime-400 font-bold text-lg">1</span>
                        </div>
                      </div>

                      {/* Instagram Posts */}
                      <div className="bg-[#262626] rounded-lg p-4 border border-gray-600">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <span className="text-2xl mr-3">📸</span>
                            <div>
                              <h4 className="text-white font-medium">Static Instagram Posts</h4>
                              <p className="text-gray-400 text-sm">Square format, high quality</p>
                            </div>
                          </div>
                          <span className="text-lime-400 font-bold text-lg">2</span>
                        </div>
                      </div>
                    </div>

                    {/* Content Requirements */}
                    <div className="bg-[#1a1a1a] rounded-lg p-4 border border-gray-600">
                      <h4 className="text-[#7EFCD8] font-medium mb-2 text-sm">Content Requirements:</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-start">
                          <span className="text-lime-400 mr-2">•</span>
                          <span className="text-gray-300">Must include required hashtags</span>
                        </div>
                        <div className="flex items-start">
                          <span className="text-lime-400 mr-2">•</span>
                          <span className="text-gray-300">Must tag @matchably.official</span>
                        </div>
                        <div className="flex items-start">
                          <span className="text-lime-400 mr-2">•</span>
                          <span className="text-gray-300">Content must be original</span>
                        </div>
                        <div className="flex items-start">
                          <span className="text-lime-400 mr-2">•</span>
                          <span className="text-gray-300">Submit within 7 days of product receipt</span>
                        </div>
                      </div>
                    </div>

                    {/* Performance Metrics */}
                    <div className="bg-[#1a1a1a] rounded-lg p-4 border border-gray-600">
                      <h4 className="text-[#7EFCD8] font-medium mb-2 text-sm">Performance Metrics:</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-400">Minimum Views:</span>
                          <span className="text-white block font-medium">1,000</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Minimum Likes:</span>
                          <span className="text-white block font-medium">50</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Engagement Rate:</span>
                          <span className="text-white block font-medium">3%+</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Post Duration:</span>
                          <span className="text-white block font-medium">30 days</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          
          {/* Left Column - Product Images */}
          <div className="lg:col-span-1">
            <div className="bg-[#262626] rounded-2xl p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-[#E0FFFA] mb-4 FontNoto">Product Gallery</h3>

              {/* Main Image */}
              <div className="relative mb-4">
                <img
                  src={campaign.productImages?.[selectedImageIndex] || campaign.productImage || '/api/placeholder/400/400'}
                  alt={campaign.productName || campaign.campaignTitle}
                  className="w-full h-80 object-cover rounded-xl"
                />
                <div className="absolute top-4 left-4 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-lg text-sm">
                  Product Sample
                </div>
              </div>

              {/* Thumbnail Images */}
              {campaign.productImages && campaign.productImages.length > 1 ? (
                <div className="flex gap-2 items-center">
                  {campaign.productImages.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                        selectedImageIndex === index
                          ? 'border-lime-400 ring-2 ring-lime-400/30'
                          : 'border-gray-600 hover:border-gray-400'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`${campaign.productName || campaign.campaignTitle} view ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                  <div className="ml-2 text-gray-400 text-xs whitespace-nowrap">
                    <span>(click to enlarge)</span>
                  </div>
                </div>
              ) : campaign.productImage ? (
                // Fallback: Show main image as thumbnail when only one image exists
                <div className="flex gap-2 items-center">
                  <div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 border-lime-400 ring-2 ring-lime-400/30">
                    <img
                      src={campaign.productImage}
                      alt={campaign.productName || campaign.campaignTitle}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 border-gray-600 bg-gray-700">
                    <img
                      src={campaign.productImage}
                      alt={`${campaign.productName || campaign.campaignTitle} alternate view`}
                      className="w-full h-full object-cover opacity-80"
                    />
                  </div>
                  <div className="ml-2 text-gray-400 text-xs whitespace-nowrap">
                    <span>(click to enlarge)</span>
                  </div>
                </div>
              ) : null}

              {/* Product Info */}
              <div className="space-y-3 mt-6">
                <h2 className="text-xl font-bold text-white FontNoto">
                  {campaign.productName || campaign.campaignTitle}
                </h2>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {campaign.productDescription || 'Premium quality product for content creation'}
                </p>

                {/* Product Value */}
                <div className="bg-[#1a1a1a] rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Estimated Value:</span>
                    <span className="text-lime-400 font-bold text-lg">
                      ${campaign.productValue || '25-50'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Campaign Details */}
          <div className="lg:col-span-2 space-y-6">

            {/* Product Highlights */}
            <div className="bg-[#262626] rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-[#E0FFFA] mb-4 FontNoto flex items-center">
                <span className="mr-2">✓</span> Product Highlights
              </h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <span className="text-[#7EFCD8] mr-2">✓</span>
                  <span className="text-gray-300">Target dark spots</span>
                </div>
                <div className="flex items-start">
                  <span className="text-[#7EFCD8] mr-2">✓</span>
                  <span className="text-gray-300">Contains Niacinamide + Hyaluronic Acid</span>
                </div>
                <div className="flex items-start">
                  <span className="text-[#7EFCD8] mr-2">✓</span>
                  <span className="text-gray-300">Suitable for all skin types</span>
                </div>
              </div>
            </div>

            {/* How to Use */}
            <div className="bg-[#262626] rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-[#E0FFFA] mb-4 FontNoto">How to Use</h3>
              <div className="space-y-2">
                <p className="text-gray-300">
                  <span className="font-medium text-white">1.</span> Apply after toner, AM & PM
                </p>
                <p className="text-gray-300">
                  <span className="font-medium text-white">2.</span> Follow with moisturizer and SPF (AM)
                </p>
              </div>
            </div>

            {/* Two Column Layout for Guidelines and Details */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

              {/* Content Creation Guidelines */}
              <div className="bg-[#262626] rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-[#E0FFFA] mb-4 FontNoto">
                  2. Content Creation Guidelines
                </h3>
                <div className="space-y-4">

                  {/* Platform */}
                  <div>
                    <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Platform: TikTok</h4>
                  </div>

                  {/* Required Hashtags */}
                  {campaign.requiredHashtags?.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Required Hashtags:</h4>
                      <div className="flex flex-wrap gap-2">
                        {campaign.requiredHashtags.map((hashtag, index) => (
                          <span
                            key={index}
                            className="bg-lime-500/20 text-lime-400 px-2 py-1 rounded text-xs font-medium"
                          >
                            {hashtag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Brand Mention */}
                  <div>
                    <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Brand @Mention:</h4>
                    <p className="text-gray-300 text-sm">@{campaign.brandName?.toLowerCase().replace(/\s+/g, '')}</p>
                  </div>

                  {/* Tone & Message Guide */}
                  <div>
                    <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Tone & Message Guide:</h4>
                    <p className="text-gray-300 text-sm">
                      Authentic, relatable content showcasing genuine results
                    </p>
                  </div>
                </div>
              </div>

              {/* Category-Specific Details */}
              <div className="bg-[#262626] rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-[#E0FFFA] mb-4 FontNoto">
                  4. Category-Specific Details
                </h3>
                <div className="space-y-4">

                  {/* Key Product Facts */}
                  <div>
                    <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Key Product Facts:</h4>
                    <div className="space-y-1">
                      <p className="text-gray-300 text-sm">• Category: {campaign.campaignIndustry}</p>
                      <p className="text-gray-300 text-sm">• Product Type: {campaign.productName || campaign.campaignTitle}</p>
                      <p className="text-gray-300 text-sm">• Brand: {campaign.brandName}</p>
                    </div>
                  </div>

                  {/* Key Ingredients */}
                  <div>
                    <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Key Ingredients: Niacinamide, Hyaluronic Acid</h4>
                  </div>

                  {/* Texture */}
                  <div>
                    <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Texture: Roll-on Ampoule</h4>
                  </div>

                  {/* Usage Timing */}
                  <div>
                    <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Usage Timing: Morning + Night</h4>
                  </div>
                </div>
              </div>
            </div>

            {/* Participation & Upload Requirements */}
            <div className="bg-[#262626] rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-[#E0FFFA] mb-4 FontNoto">
                3. Participation & Upload Requirements
              </h3>
              <div className="space-y-4">

                {/* Upload Content */}
                <div>
                  <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Upload content within 7 days</h4>
                </div>

                {/* Submission Delivery */}
                <div>
                  <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Submission delivery: 3-4 days</h4>
                </div>

                {/* Participation Requirements */}
                {campaign.participationRequirements && (
                  <div>
                    <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Requirements:</h4>
                    <p className="text-gray-300 text-sm">
                      {campaign.participationRequirements}
                    </p>
                  </div>
                )}

                {/* Content Format */}
                <div>
                  <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Content Format:</h4>
                  <div className="flex flex-wrap gap-2">
                    {campaign.contentFormat?.map((format, index) => (
                      <span
                        key={index}
                        className="bg-[#141414] border border-gray-600 text-white px-3 py-1 rounded-lg text-sm"
                      >
                        {format}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Campaign Timeline */}
                <div className="bg-[#1a1a1a] rounded-lg p-4">
                  <h4 className="text-sm font-medium text-[#7EFCD8] mb-2">Campaign Timeline:</h4>
                  <div className="space-y-2">
                    <p className="text-gray-300 text-sm">
                      <span className="font-medium text-white">Application Deadline:</span> {' '}
                      {campaign.deadline ? new Date(campaign.deadline).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) : 'Not specified'}
                    </p>
                    <p className="text-gray-300 text-sm">
                      <span className="font-medium text-white">Spots Available:</span> {' '}
                      {campaign.recruiting ? `${campaign.recruiting} creators` : 'Unlimited'}
                    </p>
                    <p className="text-gray-300 text-sm">
                      <span className="font-medium text-white">What You'll Receive:</span> {' '}
                      {campaign.influencersReceive || 'Free product provided'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Apply Button */}
            <div className="bg-[#262626] rounded-2xl p-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-[#E0FFFA] mb-2 FontNoto">Ready to Join?</h3>
                <p className="text-gray-300">Apply now to receive your free {campaign.productName || campaign.campaignTitle}</p>
              </div>

              <button className="w-full bg-lime-500 hover:bg-lime-600 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 FontNoto text-lg hover:scale-105 hover:shadow-lg">
                Apply for This Campaign
              </button>

              <div className="mt-4 text-center">
                <p className="text-gray-400 text-sm">
                  ✨ Free product provided • No paid ads required
                </p>
                <p className="text-gray-400 text-sm mt-1">
                  📦 Product ships within 3-5 business days after approval
                </p>
              </div>

              {/* Campaign Status */}
              <div className="mt-6 pt-4 border-t border-gray-600">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="text-center">
                    <span className="block text-gray-400">Campaign Status</span>
                    <span className="text-green-400 font-medium">🟢 {campaign.status || 'Active'}</span>
                  </div>
                  <div className="text-center">
                    <span className="block text-gray-400">Response Time</span>
                    <span className="text-white font-medium">Within 48 hours</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ✅ Apply Now CTA - Fixed Floating Button */}
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
        <button 
          onClick={handleApplyNow}
          className="bg-lime-500 hover:bg-lime-600 text-white font-bold py-4 px-8 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 FontNoto text-lg flex items-center gap-3"
        >
          <span className="text-2xl">✅</span>
          Apply to Campaign
        </button>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-[#1a1a1a] rounded-2xl p-8 max-w-md mx-4 border border-gray-700">
            <div className="text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h3 className="text-2xl font-bold text-[#E0FFFA] mb-2 FontNoto">Application Submitted!</h3>
              <p className="text-gray-300 mb-6">
                Your application has been successfully submitted. We'll review it and get back to you within 48 hours.
              </p>
              <div className="space-y-3 text-sm text-gray-400">
                <p>📧 Check your email for confirmation</p>
                <p>📱 You'll receive updates via email</p>
                <p>⏰ Response time: 24-48 hours</p>
              </div>
              <div className="mt-6 space-y-3">
                <button 
                  onClick={() => setShowSuccessModal(false)}
                  className="w-full bg-lime-500 hover:bg-lime-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
                >
                  Continue Browsing
                </button>
                <button 
                  onClick={() => navigate('/campaigns')}
                  className="w-full bg-transparent border border-gray-600 text-gray-300 hover:bg-gray-700 py-3 px-6 rounded-lg font-medium transition-colors"
                >
                  Back to Campaigns
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Lightbox/Modal */}
      {lightboxOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80" style={{backdropFilter: 'blur(2px)'}}>
          <div className="relative flex flex-col items-center">
            <img
              src={campaign.productImages?.[lightboxIndex] || campaign.productImage}
              alt="Enlarged"
              className="max-w-[90vw] max-h-[80vh] rounded-xl shadow-2xl border-4 border-lime-400"
              style={{boxShadow: '0 8px 32px 0 rgba(0,0,0,0.7)'}}
            />
            <button
              onClick={() => setLightboxOpen(false)}
              className="absolute top-2 right-2 bg-black/80 text-white rounded-full px-4 py-2 text-2xl font-bold hover:bg-black/90 focus:outline-none"
              aria-label="Close"
              style={{zIndex: 2}}
            >
              ×
            </button>
            {/* Prev/Next controls if multiple images */}
            {campaign.productImages && campaign.productImages.length > 1 && (
              <>
                <button
                  onClick={() => setLightboxIndex((lightboxIndex - 1 + campaign.productImages.length) % campaign.productImages.length)}
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/80 text-white rounded-full px-4 py-2 text-2xl font-bold hover:bg-black/90 focus:outline-none"
                  aria-label="Previous"
                  style={{zIndex: 2}}
                >
                  ‹
                </button>
                <button
                  onClick={() => setLightboxIndex((lightboxIndex + 1) % campaign.productImages.length)}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/80 text-white rounded-full px-4 py-2 text-2xl font-bold hover:bg-black/90 focus:outline-none"
                  aria-label="Next"
                  style={{zIndex: 2}}
                >
                  ›
                </button>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Campaign;
