const { User, referralReward, pointEventLog, campaignSubmission } = require("../database");

// 🔥 Core logic to run on every content approval
async function handleFirstContentApproval(userId, campaignId) {
  const user = await User.findById(userId);
  if (!user) return;

  const rewardPts = 100;

  // ✅ 1: Always award 100 points for any approved content
  await User.findByIdAndUpdate(userId, { $inc: { points: rewardPts } });

  // ✅ Log this approval clearly
  await pointEventLog.create({
    user: userId,
    source: "content-approved", // 👈 use fixed label
    points: rewardPts,
    campaign: campaignId,
    note: "Content approved - 100 points awarded",
  });

  // ✅ 2: Check if this was the first approved content (for referral bonus)
  const approvedContentCount = await campaignSubmission.countDocuments({
    user_id: userId,
    status: "reviewed",
  });

  if (approvedContentCount > 1) return; // Stop here if not first content

  // ✅ 3: Handle referral bonus only once
  const referral = await referralReward.findOne({ invitee: userId, rewardGiven: false });

  if (referral) {
    const referralPts = 50;

    // Referrer
    await User.findByIdAndUpdate(referral.referrer, { $inc: { points: referralPts } });
    await pointEventLog.create({
      user: referral.referrer,
      source: "referral-bonus",
      points: referralPts,
      relatedUser: userId,
      note: "Referral bonus for invitee's first approved content",
    });

    // Invitee
    await User.findByIdAndUpdate(userId, { $inc: { points: referralPts } });
    await pointEventLog.create({
      user: userId,
      source: "referral-bonus",
      points: referralPts,
      relatedUser: referral.referrer,
      note: "Referral bonus for being invited",
    });

    // Mark referral reward as used
    referral.rewardGiven = true;
    referral.approvedAt = new Date();
    await referral.save();
  }
}

module.exports = {
  handleFirstContentApproval,
};
