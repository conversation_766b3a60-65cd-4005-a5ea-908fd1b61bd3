// controllers/brand/trackingController.js
const { ShipmentTracking } = require('../../database/index');
const { sendShipmentEmail } = require('../../functions/sendEmail');
const trackingService = require('../../services/trackingService');

/**
 * POST /brand/tracking/add
 * Add shipment tracking info for a creator
 */
async function addTrackingInfo(req, res) {
  try {
    const { creatorId, campaignId, carrier, trackingNumber } = req.body;
    // Validate input
    if (!creatorId || !campaignId || !carrier || !trackingNumber) {
      return res.status(400).json({ status: 'failed', message: 'creatorId, campaignId, carrier, and trackingNumber are required' });
    }

    // Get real-time tracking info from 17track API
    let trackingInfo = null;
    try {
      const trackingResult = await trackingService.trackPackage(trackingNumber, carrier);
      if (trackingResult.success) {
        trackingInfo = trackingResult.data;
      }
    } catch (trackingError) {
      console.error('17track API error:', trackingError);
      // Continue without tracking info if API fails
    }

    // Create tracking record
    const record = await ShipmentTracking.create({
      brand: req.user.id,
      creator: creatorId,
      campaign: campaignId,
      carrier,
      trackingNumber,
      sentOn: new Date(),
      trackingInfo: trackingInfo || null
    });

    // Send notification email asynchronously
    sendShipmentEmail(creatorId, campaignId, carrier, trackingNumber)
      .catch(err => console.error('❌ sendShipmentEmail error:', err));

    return res.json({ 
      status: 'success', 
      record,
      trackingInfo: trackingInfo ? 'Real-time tracking info retrieved' : 'Tracking info will be updated later'
    });
  } catch (error) {
    console.error('❌ addTrackingInfo error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error adding tracking info' });
  }
}

/**
 * GET /brand/tracking/creator/:campaignId
 * Get tracking info for the logged-in creator
 */
async function getTrackingForCreator(req, res) {
  try {
    const { campaignId } = req.params;
    if (!campaignId) {
      return res.status(400).json({ status: 'failed', message: 'campaignId is required' });
    }

    const record = await ShipmentTracking.findOne({
      campaign: campaignId,
      creator: req.user.id
    });

    if (!record) {
      return res.json({ status: 'success', info: null });
    }

    // Get updated tracking info from 17track API
    let updatedTrackingInfo = null;
    try {
      const trackingResult = await trackingService.trackPackage(record.trackingNumber, record.carrier);
      if (trackingResult.success) {
        updatedTrackingInfo = trackingResult.data;
        
        // Update the record with new tracking info
        await ShipmentTracking.findByIdAndUpdate(record._id, {
          trackingInfo: updatedTrackingInfo,
          lastUpdated: new Date()
        });
      }
    } catch (trackingError) {
      console.error('17track API error:', trackingError);
      // Use existing tracking info if API fails
      updatedTrackingInfo = record.trackingInfo;
    }

    return res.json({ 
      status: 'success', 
      info: {
        ...record.toObject(),
        trackingInfo: updatedTrackingInfo
      }
    });
  } catch (error) {
    console.error('❌ getTrackingForCreator error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching tracking info' });
  }
}

/**
 * POST /brand/tracking/update/:trackingId
 * Update tracking info for a specific shipment
 */
async function updateTrackingInfo(req, res) {
  try {
    const { trackingId } = req.params;
    const { carrier, trackingNumber } = req.body;

    if (!trackingId) {
      return res.status(400).json({ status: 'failed', message: 'trackingId is required' });
    }

    const record = await ShipmentTracking.findById(trackingId);
    if (!record) {
      return res.status(404).json({ status: 'failed', message: 'Tracking record not found' });
    }

    // Verify brand owns this tracking record
    if (record.brand.toString() !== req.user.id) {
      return res.status(403).json({ status: 'failed', message: 'Unauthorized' });
    }

    // Get updated tracking info from 17track API
    let trackingInfo = null;
    try {
      const trackingResult = await trackingService.trackPackage(
        trackingNumber || record.trackingNumber, 
        carrier || record.carrier
      );
      if (trackingResult.success) {
        trackingInfo = trackingResult.data;
      }
    } catch (trackingError) {
      console.error('17track API error:', trackingError);
    }

    // Update the record
    const updateData = {
      lastUpdated: new Date()
    };

    if (carrier) updateData.carrier = carrier;
    if (trackingNumber) updateData.trackingNumber = trackingNumber;
    if (trackingInfo) updateData.trackingInfo = trackingInfo;

    const updatedRecord = await ShipmentTracking.findByIdAndUpdate(
      trackingId, 
      updateData, 
      { new: true }
    );

    return res.json({ 
      status: 'success', 
      record: updatedRecord,
      trackingInfo: trackingInfo ? 'Real-time tracking info updated' : 'Tracking info update failed'
    });
  } catch (error) {
    console.error('❌ updateTrackingInfo error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error updating tracking info' });
  }
}

/**
 * GET /brand/tracking/history/:trackingId
 * Get detailed tracking history for a shipment
 */
async function getTrackingHistory(req, res) {
  try {
    const { trackingId } = req.params;

    if (!trackingId) {
      return res.status(400).json({ status: 'failed', message: 'trackingId is required' });
    }

    const record = await ShipmentTracking.findById(trackingId);
    if (!record) {
      return res.status(404).json({ status: 'failed', message: 'Tracking record not found' });
    }

    // Verify user has access to this tracking record
    if (record.brand.toString() !== req.user.id && record.creator.toString() !== req.user.id) {
      return res.status(403).json({ status: 'failed', message: 'Unauthorized' });
    }

    // Get detailed tracking history from 17track API
    let trackingHistory = null;
    try {
      const historyResult = await trackingService.getTrackingHistory(record.trackingNumber, record.carrier);
      if (historyResult.success) {
        trackingHistory = historyResult.data;
      }
    } catch (trackingError) {
      console.error('17track API error:', trackingError);
    }

    return res.json({ 
      status: 'success', 
      record,
      trackingHistory
    });
  } catch (error) {
    console.error('❌ getTrackingHistory error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching tracking history' });
  }
}

module.exports = { 
  addTrackingInfo, 
  getTrackingForCreator, 
  updateTrackingInfo, 
  getTrackingHistory 
};
