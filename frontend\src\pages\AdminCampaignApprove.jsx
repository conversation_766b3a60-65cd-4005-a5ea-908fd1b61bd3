import React, { useEffect, useState } from "react";
import axios from "axios";
import config from "../config";
import <PERSON><PERSON> from "js-cookie";
import { toast } from "react-toastify";

const REJECTION_REASONS = [
  "Product image missing",
  "Content brief too vague",
  "Fixed price too low",
  "Bidding range invalid",
  "Usage rights too aggressive",
  "Other",
];

const AdminCampaignApprove = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [showConfirm, setShowConfirm] = useState(false);
  const [pendingAction, setPendingAction] = useState({ id: null, type: null });
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [customReason, setCustomReason] = useState("");

  const fetchPendingCampaigns = async () => {
    setLoading(true);
    try {
      const token = Cookie.get("AdminToken");
      const res = await axios.get(
        `${config.BACKEND_URL}/admin/campaign-approvals/pending`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      if (res.data.status === "success") {
        console.log(res.data.requests);
        setRequests(res.data.requests);
      } else {
        toast.error("Failed to load campaigns");
      }
    } catch (err) {
      console.error(err);
      toast.error("Error fetching campaigns");
    } finally {
      setLoading(false);
    }
  };

  const handleApproval = async (id, type, reason = "") => {
    try {
      const token = Cookie.get("AdminToken");
      let url = `${config.BACKEND_URL}/admin/campaign-approvals/${id}/${type}`;
      let data = {};
      if (type === "reject" && reason) {
        data.rejection_reason = reason;
      }
      const res = await axios.patch(
        url,
        data,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (res.data.status === "success") {
        toast.success(`Campaign ${type} successfully`);
        fetchPendingCampaigns();
      } else {
        toast.error(res.data.message || "Action failed");
      }
    } catch (err) {
      console.error(err);
      toast.error("Error updating campaign status");
    }
  };

  useEffect(() => {
    fetchPendingCampaigns();
  }, []);

  // Modal content
  const renderModal = () => {
    if (!selectedCampaign) return null;
    const c = selectedCampaign;
    return (
      <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
        <div className="bg-[#232323] p-8 rounded-2xl shadow-2xl max-w-2xl w-full overflow-y-auto max-h-[90vh] text-white relative border border-[#444]">
          <button
            className="absolute top-4 right-4 text-3xl text-gray-400 hover:text-white transition"
            onClick={() => setShowModal(false)}
            aria-label="Close"
          >
            &times;
          </button>
          <div className="flex items-center gap-4 mb-6">
            <img
              src={c.brandLogo}
              alt="Brand Logo"
              className="h-20 w-20 rounded-full object-cover border-2 border-[#444] shadow"
            />
            <div>
              <h2 className="text-3xl font-extrabold mb-1">
                {c.campaignTitle}
              </h2>
              <div className="flex gap-2 items-center mt-2">
                {c.pricingModel && (
                  <span className="bg-blue-700/80 text-blue-200 px-3 py-1 rounded-full text-xs font-semibold">
                    {c.pricingModel?.charAt(0).toUpperCase() +
                      c.pricingModel?.slice(1)}
                  </span>
                )}
                {!c.pricingModel && (
                  <span className="bg-blue-700/80 text-blue-200 px-3 py-1 rounded-full text-xs font-semibold">
                    Gifted
                  </span>
                )}

                <span
                  className={`px-3 py-1 rounded-full text-xs font-semibold
                  ${
                    c.status === "Pending Review"
                      ? "bg-yellow-700/80 text-yellow-200"
                      : "bg-green-700/80 text-green-200"
                  }
                `}
                >
                  {c.status}
                </span>
                <span
                  className={`px-3 py-1 rounded-full text-xs font-semibold
                  ${
                    c.approvalStatus === "Pending"
                      ? "bg-gray-700/80 text-gray-200"
                      : c.approvalStatus === "Approved"
                      ? "bg-green-700/80 text-green-200"
                      : "bg-red-700/80 text-red-200"
                  }
                `}
                >
                  {c.approvalStatus}
                </span>
              </div>
              <div className="text-sm text-gray-400 mt-1">{c.brandName}</div>
            </div>
          </div>
          <div className="border-t border-[#444] pt-4 grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3">
            <div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">Industry:</span>{" "}
                {c.campaignIndustry}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">Product:</span>{" "}
                {c.productName}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Description:
                </span>{" "}
                {c.productDescription}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Content Format:
                </span>{" "}
                {c.contentFormat?.join(", ")}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Creator Count:
                </span>{" "}
                {c.creatorCount}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Required Hashtags:
                </span>{" "}
                {c.requiredHashtags?.join(", ")}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Mention Handle:
                </span>{" "}
                {c.mentionHandle}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">Tone Guide:</span>{" "}
                {c.toneGuide || "N/A"}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Participation Requirements:
                </span>{" "}
                {c.participationRequirements}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Requested Content:
                </span>
                <span className="ml-1">
                  Videos: {c.requestedContent?.videos || 0}, Photos:{" "}
                  {c.requestedContent?.photos || 0}
                </span>
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">Notes:</span>{" "}
                {c.requestedContent?.notes || "N/A"}
              </div>
            </div>
            <div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Product Images:
                </span>
                <div className="flex gap-2 mt-1 flex-wrap">
                  {c.productImages?.map((img, idx) => (
                    <img
                      key={idx}
                      src={img}
                      alt="Product"
                      className="h-16 w-16 rounded-lg object-cover border border-[#444] shadow"
                    />
                  ))}
                </div>
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Usage Rights:
                </span>{" "}
                {c.usageTerms?.usageRights?.join(", ")}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Usage Rights Duration:
                </span>{" "}
                {c.usageTerms?.usageRightsDuration || "N/A"}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Late Submission Penalty:
                </span>{" "}
                {c.usageTerms?.lateSubmissionPenalty || "N/A"}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Payment Responsibility Notice:
                </span>{" "}
                {c.usageTerms?.paymentResponsibilityNotice ? "Yes" : "No"}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">Deadline:</span>{" "}
                {new Date(c.deadline).toLocaleDateString()}
              </div>
              <div className="mb-2">
                <span className="font-semibold text-gray-300">
                  Recruitment End Date:
                </span>{" "}
                {new Date(c.recruitmentEndDate).toLocaleDateString()}
              </div>
              {c.pricingModel && (
                <div className="mb-2">
                  <span className="font-semibold text-gray-300">
                    Pricing Model:
                  </span>{" "}
                  {c.pricingModel}
                </div>
              )}
              {c.pricingModel && (
                <div className="mb-2">
                  <span className="font-semibold text-gray-300">Price:</span>{" "}
                  {c.pricingModel === "fixed" && c.fixedPrice && (
                    <span className="text-green-300 font-semibold">
                      ${c.fixedPrice} per creator
                    </span>
                  )}
                  {c.pricingModel === "bidding" && (
                    <span className="text-blue-300 font-semibold">
                      Min ${c.minBid} / Max ${c.maxBid}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-8 min-h-screen bg-gradient-to-br from-[#181818] to-[#232323] text-white">
      <h2 className="text-3xl font-extrabold mb-8 tracking-tight">
        Pending Campaign Approvals
      </h2>
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <span className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></span>
        </div>
      ) : requests.length === 0 ? (
        <div className="text-center text-gray-400 mt-16 text-lg">
          No pending campaigns.
        </div>
      ) : (
        <div className="grid md:grid-cols-2 gap-6">
          {requests.map((campaign) => (
            <div
              key={campaign._id}
              className="bg-[#232323] p-6 rounded-2xl shadow-lg border border-[#333] hover:shadow-2xl transition-shadow duration-300 relative group"
            >
              <div className="flex items-center gap-4 mb-4">
                <img
                  src={campaign.brandLogo}
                  alt="Brand Logo"
                  className="h-14 w-14 rounded-full object-cover border-2 border-[#444]"
                />
                <div>
                  <h3 className="text-xl font-bold">
                    {campaign.campaignTitle}
                  </h3>
                  <span className="text-xs text-gray-400">
                    {campaign.brandName}
                  </span>
                </div>
                {campaign.pricingModel && (
                  <span
                    className={`ml-auto px-3 py-1 rounded-full text-xs font-semibold
                ${
                  campaign.pricingModel === "fixed"
                    ? "bg-green-700/80 text-green-200"
                    : "bg-blue-700/80 text-blue-200"
                }
              `}
                  >
                    {campaign.pricingModel?.charAt(0).toUpperCase() +
                      campaign.pricingModel?.slice(1)}
                  </span>
                )}
              </div>
              <div className="mb-2 flex flex-wrap gap-2">
                <span className="bg-gray-700/60 px-2 py-1 rounded text-xs">
                  <strong>Deadline:</strong>{" "}
                  {new Date(campaign.deadline).toLocaleDateString()}
                </span>
                <span className="bg-gray-700/60 px-2 py-1 rounded text-xs">
                  <strong>Creator Count:</strong> {campaign.creatorCount}
                </span>
                <span className="bg-gray-700/60 px-2 py-1 rounded text-xs">
                  <strong>Status:</strong> {campaign.status}
                </span>
              </div>
              <div className="mb-2">
                {campaign.pricingModel && (
                  <span className="inline-block bg-gradient-to-r from-pink-600 to-purple-600 text-white px-2 py-1 rounded text-xs font-semibold">
                    {campaign.pricingModel === "fixed" &&
                      campaign.fixedPrice && (
                        <>${campaign.fixedPrice} per creator</>
                      )}
                    {campaign.pricingModel === "bidding" && (
                      <>
                        Min ${campaign.minBid} / Max ${campaign.maxBid}
                      </>
                    )}
                  </span>
                )}
                {!campaign.pricingModel && (
                  <span className="inline-block bg-gradient-to-r from-pink-600 to-purple-600 text-white px-2 py-1 rounded text-xs font-semibold">
                    Gifted
                  </span>
                )}
              </div>
              <div className="mb-2">
                <span className="text-sm text-gray-300">
                  <strong>Usage Rights:</strong>{" "}
                  {campaign.usageTerms?.usageRights?.join(", ")}
                </span>
              </div>
              <div className="mb-2">
                <span className="text-sm text-gray-300">
                  <strong>Description:</strong> {campaign.productDescription}
                </span>
              </div>
              <div className="flex gap-3 mt-4">
                <button
                  onClick={() => {
                    setPendingAction({ id: campaign._id, type: "approve" });
                    setShowConfirm(true);
                  }}
                  className="flex-1 bg-gradient-to-r from-green-500 to-green-700 hover:from-green-600 hover:to-green-800 text-white font-semibold py-2 rounded-lg shadow transition"
                >
                  Approve
                </button>
                <button
                  onClick={() => {
                    setPendingAction({ id: campaign._id, type: "reject", campaign });
                    if (campaign.pricingModel) {
                      setRejectReason("");
                      setCustomReason("");
                      setShowRejectModal(true);
                    } else {
                      setShowConfirm(true);
                    }
                  }}
                  className="flex-1 bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 text-white font-semibold py-2 rounded-lg shadow transition"
                >
                  Reject
                </button>
                <button
                  className="flex-1 bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white font-semibold py-2 rounded-lg shadow transition flex items-center justify-center gap-2"
                  onClick={() => {
                    setSelectedCampaign(campaign);
                    setShowModal(true);
                  }}
                >
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
      {showModal && renderModal()}
      {showConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
          <div className="bg-[#232323] p-8 rounded-2xl shadow-2xl text-white max-w-sm w-full border border-[#444]">
            <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
              Confirm{" "}
              {pendingAction.type === "approve" ? "Approval" : "Rejection"}
            </h3>
            <p className="mb-6 text-gray-300">
              Are you sure you want to{" "}
              <span className="font-semibold">{pendingAction.type}</span> this
              campaign?
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg transition"
                onClick={() => setShowConfirm(false)}
              >
                Cancel
              </button>
              <button
                className={
                  pendingAction.type === "approve"
                    ? "bg-gradient-to-r from-green-500 to-green-700 hover:from-green-600 hover:to-green-800 px-4 py-2 rounded-lg font-semibold transition"
                    : "bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 px-4 py-2 rounded-lg font-semibold transition"
                }
                onClick={() => {
                  handleApproval(pendingAction.id, pendingAction.type);
                  setShowConfirm(false);
                }}
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
          <div className="bg-[#232323] p-8 rounded-2xl shadow-2xl text-white max-w-sm w-full border border-[#444]">
            <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
              Reject Campaign
            </h3>
            <p className="mb-4 text-gray-300">
              Please select a reason for rejection:
            </p>
            <select
              className="w-full p-2 mb-4 rounded bg-[#181818] border border-[#444] text-white"
              value={rejectReason}
              onChange={e => setRejectReason(e.target.value)}
            >
              <option value="">Select a reason...</option>
              {REJECTION_REASONS.map(r => (
                <option key={r} value={r}>{r}</option>
              ))}
            </select>
            {rejectReason === "Other" && (
              <textarea
                className="w-full p-2 mb-4 rounded bg-[#181818] border border-[#444] text-white"
                maxLength={200}
                placeholder="Enter custom reason (max 200 chars)"
                value={customReason}
                onChange={e => setCustomReason(e.target.value)}
              />
            )}
            <div className="flex justify-end gap-3">
              <button
                className="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg transition"
                onClick={() => setShowRejectModal(false)}
              >
                Cancel
              </button>
              <button
                className="bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 px-4 py-2 rounded-lg font-semibold transition"
                disabled={(!rejectReason || (rejectReason === "Other" && !customReason.trim()))}
                onClick={() => {
                  const reason = rejectReason === "Other" ? customReason.trim() : rejectReason;
                  handleApproval(pendingAction.id, "reject", reason);
                  setShowRejectModal(false);
                }}
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminCampaignApprove;
