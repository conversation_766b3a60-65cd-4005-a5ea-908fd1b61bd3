const express = require("express");
const { handleFirstContentApproval } = require("../../services/pointService");
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");
const { appliedCampaigns, Campaign } = require("../../database");
const { Parser } = require("json2csv");
const mongoose = require("mongoose");
const application = express.Router();
const { sendContentSubmissionReminderEmail } = require("../../functions/sendEmail");

application.get("/:id", VerifyTokenAuth, async (req, res) => {
  try {
    const limit = 100;
    const cursor = req.query.cursor;
    const { id } = req.params;

    let query = { campaign: id };
    if (cursor) {
      query._id = { $gt: cursor };
    }

    // ✅ Step 1: Find campaign (only one, not array)
    const campaign = await Campaign.findById(id);
    if (!campaign) {
      return res.status(404).json({ status: "failed", message: "Campaign not found" });
    }

    // ✅ Step 2: Fetch paginated applications
    const results = await appliedCampaigns
      .find(query)
      .sort({ _id: 1 })
      .limit(limit + 1);

    const hasMore = results.length > limit;
    const limitedResults = hasMore ? results.slice(0, limit) : results;

    // ✅ Step 3: Format applications
    const applications = limitedResults.map((item) => ({
      id: item._id,
      name: item.name,
      email: item.email,
      phone: item.phone,
      address: `${item.address}, ${item.city}, ${item.state}, ${item.zipCode}`,
      appliedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
      status: item.status,
      rejectionReason: item.rejectionReason,
      showReasonToInfluencer: item.showReasonToInfluencer,
    }));

    // ✅ Step 4: Count approved applications
    const approvedCount = await appliedCampaigns.countDocuments({
      campaign: id,
      status: "Approved",
    });

    // ✅ Step 5: Return everything
    res.json({
      status: "success",
      applications,
      nextCursor: hasMore ? limitedResults[limitedResults.length - 1]._id : null,
      isLastPage: !hasMore,
      campaignTitle: campaign.campaignTitle,
      recruitingLimit: campaign.recruiting, // ✅ for frontend check
      approvedCount,     
      campaignType: campaign.campaignType,
      rights: campaign.usageTerms.usageRights                   // ✅ for frontend check
    });

  } catch (error) {
    console.error("Error fetching applications:", error);
    res.status(500).json({ message: "Something went wrong", error });
  }
});

application.post("/paginate/:id", VerifyTokenAuth, async (req, res) => {
  try {
    const limit = 50;
    const { LastId } = req.body;
    const {id} = req.params
    const ObjectId = mongoose.Types.ObjectId;
    let query = {
      campaign : id
    };
    if (LastId) {
      query._id = { $gt: new ObjectId(LastId.toString()) };
    }

    const results = await appliedCampaigns
      .find(query)
      .sort({ _id: 1 })
      .limit(limit + 1)

    const hasMore = results.length > limit;
    const limitedResults = hasMore ? results.slice(0, limit) : results;

    const applications = limitedResults.map((item) => ({
      id: item._id,
      name: item.name,
      email: item.email,
      phone: item.phone,
      address: `${item.address}, ${item.city}, ${item.state}, ${item.zipCode}`,
      appliedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
    }));

    console.log(applications)

    res.json({
      status: "success",
      applications,
      nextCursor: hasMore
        ? limitedResults[limitedResults.length - 1]._id
        : null,
      isLastPage: !hasMore,
    });
  } catch (error) {
    res.status(500).json({ message: "Something went wrong", error });
  }
});

application.get("/download/:id", async (req, res) => {
  try {
    const {id} = req.params
    const result = await appliedCampaigns.find({campaign: id}).sort({
      createdAt: -1,
    });
    const applications = await result.map((item) => {
      return {
        campaign: `https://machably.com/campaign/${item.campaign}`,
        name: item.name,
        email: item.email,
        phone: item.phone,
        address: `${item.address}, ${item.city}, ${item.state}, ${item.zipCode}`,
        appliedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
      };
    });


    const fields = ["campaign", "name", "email", "address", "appliedAt"];
    const opts = {
      fields,
      quote: '"', // wrap all values in double quotes
      delimiter: ",", // standard comma-delimiter
    };

    const json2csvParser = new Parser(opts);
    const csv = json2csvParser.parse(applications);

    res.header("Content-Type", "text/csv");
    res.attachment("applications.csv");
    res.send(csv);
  } catch {
    res.json({
      status: "failed",
      message: "something went wrong",
    });
  }
});
module.exports = {
  application,
};

application.patch("/:applicationId/status", VerifyTokenAuth, async (req, res) => {
  const { applicationId } = req.params;
  const { status, rejectionReason, showReasonToInfluencer } = req.body;

  if (!["Approved", "Rejected", "Pending"].includes(status)) {
    return res.status(400).json({ status: "failed", message: "Invalid status value" });
  }

  try {
    const application = await appliedCampaigns.findById(applicationId);
    if (!application) {
      return res.status(404).json({ status: "failed", message: "Application not found" });
    }

    let campaign = null;

    // ✅ Approval logic
    if (status === "Approved") {
      campaign = await Campaign.findById(application.campaign);
      if (!campaign) {
        return res.status(404).json({ status: "failed", message: "Associated campaign not found" });
      }

      const approvedCount = await appliedCampaigns.countDocuments({
        campaign: campaign._id,
        status: "Approved",
      });

      if (approvedCount >= campaign.recruiting) {
        return res.status(409).json({
          status: "failed",
          message: `Recruiting limit full. ${approvedCount} / ${campaign.recruiting} seats filled.`,
        });
      }
    }

    // ✏️ Update status and rejection info
    application.status = status;

    if (status === "Rejected") {
      application.rejectionReason = rejectionReason || "";
      application.showReasonToInfluencer = !!showReasonToInfluencer;
    } else {
      application.rejectionReason = "";
      application.showReasonToInfluencer = false;
    }

    await application.save();

    // 🎯 Reward + Email on Approval
    if (status === "Approved") {
      await handleFirstContentApproval(application.user, application.campaign);
      console.log("📨 Sending approval email for campaign:", campaign.campaignTitle);
      await sendContentSubmissionReminderEmail(application.email, application.name, campaign.campaignTitle);
    }

    return res.status(200).json({
      status: "success",
      message: `Application marked as ${status}`,
      application,
    });
  } catch (err) {
    console.error("❌ Error updating application status:", err);
    return res.status(500).json({ status: "failed", message: "Server error" });
  }
});

application.delete("/:campaignId/:applicantId", VerifyTokenAuth, async (req, res) => {
  try {
    const { applicantId } = req.params;
    const deletedApplicant = await appliedCampaigns.findByIdAndDelete(applicantId);

    if (!deletedApplicant) {
      return res.status(404).json({
        status: "error",
        message: "Applicant not found",
      });
    }

    res.status(200).json({
      status: "success",
      message: "Applicant deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting applicant:", error);
    res.status(500).json({
      status: "error",
      message: "Server error while deleting applicant",
    });
  }
});

