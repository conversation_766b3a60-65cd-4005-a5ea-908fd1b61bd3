const axios = require('axios');

class TrackingService {
    constructor() {
        this.baseURL = 'https://api.17track.net/en';
        this.apiKey = process.env.TRACKING_API_KEY || 'your-api-key-here';
    }

    /**
     * Track a single package by tracking number
     * @param {string} trackingNumber - The tracking number to look up
     * @param {string} carrier - Optional carrier code
     * @returns {Promise<Object>} Tracking information
     */
    async trackPackage(trackingNumber, carrier = null) {
        try {
            const payload = {
                "number": trackingNumber
            };

            if (carrier) {
                payload.carrier = carrier;
            }

            const response = await axios.post(`${this.baseURL}/track`, payload, {
                headers: {
                    'Content-Type': 'application/json',
                    '17token': this.apiKey
                }
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('Tracking API Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Track multiple packages at once
     * @param {Array<string>} trackingNumbers - Array of tracking numbers
     * @param {string} carrier - Optional carrier code for all packages
     * @returns {Promise<Object>} Tracking information for all packages
     */
    async trackMultiplePackages(trackingNumbers, carrier = null) {
        try {
            const payload = {
                "number": trackingNumbers
            };

            if (carrier) {
                payload.carrier = carrier;
            }

            const response = await axios.post(`${this.baseURL}/track`, payload, {
                headers: {
                    'Content-Type': 'application/json',
                    '17token': this.apiKey
                }
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('Tracking API Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Get supported carriers
     * @returns {Promise<Object>} List of supported carriers
     */
    async getSupportedCarriers() {
        try {
            const response = await axios.get(`${this.baseURL}/carrier`, {
                headers: {
                    '17token': this.apiKey
                }
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('Carrier API Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Get tracking history for a package
     * @param {string} trackingNumber - The tracking number
     * @param {string} carrier - Optional carrier code
     * @returns {Promise<Object>} Tracking history
     */
    async getTrackingHistory(trackingNumber, carrier = null) {
        try {
            const payload = {
                "number": trackingNumber
            };

            if (carrier) {
                payload.carrier = carrier;
            }

            const response = await axios.post(`${this.baseURL}/track/history`, payload, {
                headers: {
                    'Content-Type': 'application/json',
                    '17token': this.apiKey
                }
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('Tracking History API Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Get real-time tracking updates
     * @param {string} trackingNumber - The tracking number
     * @param {string} carrier - Optional carrier code
     * @returns {Promise<Object>} Real-time tracking information
     */
    async getRealTimeTracking(trackingNumber, carrier = null) {
        try {
            const payload = {
                "number": trackingNumber
            };

            if (carrier) {
                payload.carrier = carrier;
            }

            const response = await axios.post(`${this.baseURL}/track/realtime`, payload, {
                headers: {
                    'Content-Type': 'application/json',
                    '17token': this.apiKey
                }
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('Real-time Tracking API Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Parse tracking number to detect carrier
     * @param {string} trackingNumber - The tracking number to parse
     * @returns {Promise<Object>} Carrier detection result
     */
    async detectCarrier(trackingNumber) {
        try {
            const response = await axios.post(`${this.baseURL}/detect`, {
                "number": trackingNumber
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    '17token': this.apiKey
                }
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('Carrier Detection API Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }
}

module.exports = new TrackingService(); 