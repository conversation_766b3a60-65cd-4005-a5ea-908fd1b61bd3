# 17track API Integration Setup

This guide explains how to set up the 17track API integration for package tracking functionality.

## Environment Variables

Add the following environment variable to your `.env` file:

```env
TRACKING_API_KEY=your-17track-api-key-here
```

## Getting Your 17track API Key

1. Visit [17track API Documentation](https://api.17track.net/en/doc?version=v2.2)
2. Sign up for an account
3. Generate your API key
4. Add the API key to your `.env` file

## API Endpoints

### Brand Tracking Endpoints

- `POST /api/brand/tracking/add` - Add tracking info for a creator
- `GET /api/brand/tracking/creator/:campaignId` - Get tracking info for creator
- `POST /api/brand/tracking/update/:trackingId` - Update tracking info
- `GET /api/brand/tracking/history/:trackingId` - Get detailed tracking history

### General Tracking Endpoints

- `POST /api/tracking/track` - Track a single package
- `POST /api/tracking/track-multiple` - Track multiple packages
- `GET /api/tracking/carriers` - Get supported carriers
- `POST /api/tracking/history` - Get tracking history
- `POST /api/tracking/realtime` - Get real-time tracking updates
- `POST /api/tracking/detect-carrier` - Detect carrier from tracking number

## Features

### Real-time Tracking
- Automatic tracking info retrieval when adding new shipments
- Real-time updates when viewing tracking information
- Detailed tracking history with 17track API data

### Supported Carriers
The 17track API supports 1000+ carriers worldwide including:
- UPS
- FedEx
- DHL
- USPS
- Amazon Logistics
- And many more

### Error Handling
- Graceful fallback if 17track API is unavailable
- Detailed error logging
- User-friendly error messages

## Usage Examples

### Adding Tracking Info
```javascript
const response = await fetch('/api/brand/tracking/add', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    creatorId: 'creator-id',
    campaignId: 'campaign-id',
    carrier: 'UPS',
    trackingNumber: '1Z999AA1234567890'
  })
});
```

### Getting Tracking Info
```javascript
const response = await fetch('/api/brand/tracking/creator/campaign-id', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## Database Schema Updates

The `ShipmentTracking` model has been updated with new fields:
- `trackingInfo`: Stores real-time tracking data from 17track API
- `lastUpdated`: Timestamp of last tracking info update

## Testing

You can test the tracking functionality with sample tracking numbers:
- UPS: `1Z999AA1234567890`
- FedEx: `123456789012`
- USPS: `9400100000000000000000`

Note: Use real tracking numbers for production testing. 