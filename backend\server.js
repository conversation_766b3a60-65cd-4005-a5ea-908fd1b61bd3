/** @format */

const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet'); // ✅ added helmet
const rateLimit = require('express-rate-limit'); // ✅ global rate limiter
const cloudinary = require('cloudinary').v2;
const multer = require('multer');
const mongoose = require('mongoose');
const path = require('path');

// Load .env file explicitly
dotenv.config({ path: path.join(__dirname, '.env') });

// Existing routes
const { Auth } = require('./routes/Auth');
const { AdminAuth } = require('./routes/Admin/Auth');
const { Campaigns } = require('./routes/Admin/campaigns');
const { application } = require('./routes/Admin/Applications');
const { registered } = require('./routes/Admin/registered');
const { adminRouter } = require('./routes/Admin/rewardsAdmin');
const { user } = require('./routes/user/user');
const { CampaignsPublic } = require('./middlewares/public');

const campaignRoutes = require('./routes/user/campaignsubmission');
const adminBrandApproveRoutes = require('./routes/Admin/brands');
const adminPaymentRoutes = require('./routes/Admin/payments');

// Brand payment routes
const brandPaymentRoutes = require('./routes/brand/payments');

// New brand routes
const brandAuthRouter = require('./routes/brand/auth');
const brandPackageRouter = require('./routes/brand/package');
const brandDashboardRouter = require('./routes/brand/dashboard');
const brandTrackingRouter = require('./routes/brand/tracking');
const brandApplicationRoutes = require('./routes/brand/applications');
const brandCampaignRequestRoutes = require('./routes/brand/campaignRequest');
const adminCampaignApprovalRoutes = require('./routes/Admin/campaignRequest');
const brandSettingsRouter = require('./routes/brand/settings');
const brandCampaignApplyRoutes = require('./routes/brand/brandCampaignApplyRoutes');
const brandSubmissionRoutes = require("./routes/brand/brandSubmissionRoutes");
const { brandUsers } = require("./routes/brand/brandUsers");

// Tracking routes
const trackingRoutes = require('./routes/tracking');

// Webhook route
const webhooks = require('./routes/brand/webhooks');

// File upload setup
const upload = multer({ dest: 'uploads/' });

// Fix CORS configuration
const allowedOrigins = process.env.FRONTEND_URL ? process.env.FRONTEND_URL.split(',') : [
  'http://localhost:3000',
  'http://localhost:3001', 
  'http://localhost:5173',
  'http://127.0.0.1:3000',
  'http://127.0.0.1:3001',
  'http://127.0.0.1:5173'
];

const PORT = process.env.PORT || 2340;

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

const app = express();

// ✅ Helmet (security headers)
app.use(helmet());

// ✅ Global rate limiter (200 requests/min per IP)
const globalLimiter = rateLimit({
  windowMs: 60 * 1000,
  max: 200,
  message: { status: 'failed', message: 'Too many requests. Try again in 1 minute.' }
});
app.use(globalLimiter);

// ✅ Fixed CORS setup
app.use(
  cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        console.log('CORS blocked origin:', origin);
        callback(null, true); // Allow all origins for development
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  })
);

// ✅ Stripe webhook must be mounted before JSON parsing
app.use('/webhooks', webhooks);

// ✅ JSON body parsing
app.use(express.json());

// ✅ Root test route
app.get('/', (req, res) => {
  res.json({ app: 'app is live' });
});

// ✅ Existing API routes
app.use('/api/auth', Auth);
app.use('/api/admin', AdminAuth);
app.use('/api/admin/campaigns', Campaigns);
app.use('/api/admin/applications', application);

app.use('/api/admin/users', registered);
app.use('/api/admin/referrel', adminRouter);

app.use('/api/user/campaigns', CampaignsPublic);
app.use('/api/user/campaigns', user);
app.use('/api/user', campaignRoutes);
app.use('/api/admin/brands', adminBrandApproveRoutes);

// ✅ Admin payment routes
app.use('/api/admin/payments', adminPaymentRoutes);

// ✅ Brand-specific routes
app.use('/api/brand/auth', brandAuthRouter);
app.use('/api/brand/package', brandPackageRouter);
app.use('/api/brand/dashboard', brandDashboardRouter);
app.use('/api/brand/tracking', brandTrackingRouter);
app.use('/api/brand/payments', brandPaymentRoutes);
app.use('/api/brand/campaign-request', brandCampaignRequestRoutes);
app.use('/api/brand/applications', brandApplicationRoutes);
app.use('/api/brand', brandCampaignApplyRoutes);
app.use("/api", brandSubmissionRoutes);
app.use("/api/brand/users", brandUsers);

// ✅ Admin campaign approvals
app.use('/api/admin/campaign-approvals', adminCampaignApprovalRoutes);

// ✅ Brand settings
app.use('/api/brand/settings', brandSettingsRouter);

// ✅ Tracking routes
app.use('/api/tracking', trackingRoutes);

// ✅ File upload route
app.post('/api/upload', upload.single('image'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded' });
  }

  try {
    const result = await cloudinary.uploader.upload(req.file.path);
    res.status(200).json({ imageUrl: result.secure_url });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Error uploading to Cloudinary' });
  }
});

// ✅ MongoDB connection & start server
mongoose
  .connect(process.env.MONGO_URL)
  .then(() => {
    console.log('✅ Connected to MongoDB');
    app.listen(PORT, () => {
      console.log('🚀 Server running on port', PORT);
    });
  })
  .catch((err) => {
    console.error('❌ MongoDB connection failed:', err);
  });
