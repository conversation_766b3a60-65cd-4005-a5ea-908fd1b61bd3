const express = require('express');
const router = express.Router();
const trackingService = require('../services/trackingService');
const { VerifyToken } = require('../middlewares/auth');

/**
 * @route POST /api/tracking/track
 * @desc Track a single package
 * @access Private
 */
router.post('/track', VerifyToken, async (req, res) => {
    try {
        const { trackingNumber, carrier } = req.body;

        if (!trackingNumber) {
            return res.status(400).json({
                success: false,
                message: 'Tracking number is required'
            });
        }

        const result = await trackingService.trackPackage(trackingNumber, carrier);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Tracking route error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route POST /api/tracking/track-multiple
 * @desc Track multiple packages
 * @access Private
 */
router.post('/track-multiple', VerifyToken, async (req, res) => {
    try {
        const { trackingNumbers, carrier } = req.body;

        if (!trackingNumbers || !Array.isArray(trackingNumbers) || trackingNumbers.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Tracking numbers array is required'
            });
        }

        const result = await trackingService.trackMultiplePackages(trackingNumbers, carrier);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Multiple tracking route error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route GET /api/tracking/carriers
 * @desc Get supported carriers
 * @access Private
 */
router.get('/carriers', VerifyToken, async (req, res) => {
    try {
        const result = await trackingService.getSupportedCarriers();
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Carriers route error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route POST /api/tracking/history
 * @desc Get tracking history for a package
 * @access Private
 */
router.post('/history', VerifyToken, async (req, res) => {
    try {
        const { trackingNumber, carrier } = req.body;

        if (!trackingNumber) {
            return res.status(400).json({
                success: false,
                message: 'Tracking number is required'
            });
        }

        const result = await trackingService.getTrackingHistory(trackingNumber, carrier);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Tracking history route error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route POST /api/tracking/realtime
 * @desc Get real-time tracking updates
 * @access Private
 */
router.post('/realtime', VerifyToken, async (req, res) => {
    try {
        const { trackingNumber, carrier } = req.body;

        if (!trackingNumber) {
            return res.status(400).json({
                success: false,
                message: 'Tracking number is required'
            });
        }

        const result = await trackingService.getRealTimeTracking(trackingNumber, carrier);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Real-time tracking route error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route POST /api/tracking/detect-carrier
 * @desc Detect carrier from tracking number
 * @access Private
 */
router.post('/detect-carrier', VerifyToken, async (req, res) => {
    try {
        const { trackingNumber } = req.body;

        if (!trackingNumber) {
            return res.status(400).json({
                success: false,
                message: 'Tracking number is required'
            });
        }

        const result = await trackingService.detectCarrier(trackingNumber);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Carrier detection route error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

module.exports = router; 