const trackingService = require('./services/trackingService');

async function testTrackingService() {
    console.log('🧪 Testing 17track API Integration...\n');

    // Test 1: Track a single package
    console.log('1. Testing single package tracking...');
    try {
        const result = await trackingService.trackPackage('1Z999AA1234567890', 'ups');
        console.log('✅ Single tracking result:', result.success ? 'Success' : 'Failed');
        if (result.success) {
            console.log('   Data received:', Object.keys(result.data || {}));
        } else {
            console.log('   Error:', result.error);
        }
    } catch (error) {
        console.log('❌ Single tracking error:', error.message);
    }

    console.log('\n2. Testing carrier detection...');
    try {
        const result = await trackingService.detectCarrier('1Z999AA1234567890');
        console.log('✅ Carrier detection result:', result.success ? 'Success' : 'Failed');
        if (result.success) {
            console.log('   Detected carrier:', result.data);
        } else {
            console.log('   Error:', result.error);
        }
    } catch (error) {
        console.log('❌ Carrier detection error:', error.message);
    }

    console.log('\n3. Testing supported carriers...');
    try {
        const result = await trackingService.getSupportedCarriers();
        console.log('✅ Supported carriers result:', result.success ? 'Success' : 'Failed');
        if (result.success) {
            console.log('   Number of carriers:', result.data?.length || 0);
        } else {
            console.log('   Error:', result.error);
        }
    } catch (error) {
        console.log('❌ Supported carriers error:', error.message);
    }

    console.log('\n4. Testing multiple package tracking...');
    try {
        const result = await trackingService.trackMultiplePackages(['1Z999AA1234567890', '123456789012'], 'ups');
        console.log('✅ Multiple tracking result:', result.success ? 'Success' : 'Failed');
        if (result.success) {
            console.log('   Packages tracked:', result.data?.length || 0);
        } else {
            console.log('   Error:', result.error);
        }
    } catch (error) {
        console.log('❌ Multiple tracking error:', error.message);
    }

    console.log('\n📝 Note: If you see API errors, make sure to:');
    console.log('   1. Set TRACKING_API_KEY in your .env file');
    console.log('   2. Get a valid API key from https://api.17track.net/en/doc?version=v2.2');
    console.log('   3. Use real tracking numbers for production testing');
}

// Run the test
testTrackingService().catch(console.error); 