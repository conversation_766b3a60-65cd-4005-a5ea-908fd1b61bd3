import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Cookies from 'js-cookie';
import config from '../../config';
import { toast } from 'react-toastify';

const BrandPlanManagement = () => {
  const [brands, setBrands] = useState([]);
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [selectedBrandHistory, setSelectedBrandHistory] = useState([]);
  const [selectedBrandName, setSelectedBrandName] = useState('');
  
  const token = Cookies.get('AdminToken') || localStorage.getItem('token');

  // Fetch all brands with their current subscriptions
  const fetchBrands = async () => {
    try {
      const res = await axios.get(`${config.BACKEND_URL}/api/admin/brand-subscriptions`, {
        headers: { Authorization: token },
      });
      setBrands(res.data.brands || []);
    } catch (err) {
      console.error("Error fetching brands", err);
      toast.error("Failed to fetch brands");
    }
  };

  // Fetch all available plans
  const fetchPlans = async () => {
    try {
      const res = await axios.get(`${config.BACKEND_URL}/api/admin/plans`, {
        headers: { Authorization: token },
      });
      setPlans(res.data.plans || []);
    } catch (err) {
      console.error("Error fetching plans", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBrands();
    fetchPlans();
  }, []);

  // Handle plan change
  const handlePlanChange = (brandId, newPlanId) => {
    setBrands(brands.map(brand => 
      brand._id === brandId 
        ? { ...brand, selectedPlan: newPlanId }
        : brand
    ));
  };

  // Handle validity change
  const handleValidityChange = (brandId, newValidity) => {
    setBrands(brands.map(brand => 
      brand._id === brandId 
        ? { ...brand, newValidity: newValidity }
        : brand
    ));
  };

  // Save changes
  const handleSave = async (brandId) => {
    const brand = brands.find(b => b._id === brandId);
    if (!brand) return;

    try {
      const payload = {
        planId: brand.selectedPlan || brand.currentPlan?._id,
        validityDate: brand.newValidity || brand.currentValidity
      };

      await axios.patch(
        `${config.BACKEND_URL}/api/admin/brand-subscriptions/${brandId}`,
        payload,
        { headers: { Authorization: token } }
      );

      toast.success("Changes saved successfully!");
      fetchBrands(); // Refresh data
    } catch (err) {
      console.error("Error saving changes", err);
      toast.error("Failed to save changes");
    }
  };

  // View plan history
  const handleViewHistory = async (brandId, brandName) => {
    try {
      const res = await axios.get(
        `${config.BACKEND_URL}/api/admin/brand-subscriptions/${brandId}/history`,
        { headers: { Authorization: token } }
      );
      setSelectedBrandHistory(res.data.history || []);
      setSelectedBrandName(brandName);
      setShowHistoryModal(true);
    } catch (err) {
      console.error("Error fetching history", err);
      toast.error("Failed to fetch plan history");
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const formatDateRange = (start, end) => {
    return `${formatDate(start)} ~ ${formatDate(end)}`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#121212] text-white">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#121212] text-white p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Brand Plan Management</h1>
        
        <div className="bg-[#1a1a1a] rounded-lg overflow-hidden shadow-lg">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-[#2a2a2a]">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Brand Name
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Current Plan
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Change Plan
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Current Validity
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Set Validity
                  </th>
                  <th className="px-6 py-4 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Save
                  </th>
                  <th className="px-6 py-4 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Plan History
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {brands.map((brand) => (
                  <tr key={brand._id} className="hover:bg-[#2a2a2a] transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-white">
                        {brand.companyName || 'N/A'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        {brand.currentPlan?.name || 'No Plan'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <select
                        value={brand.selectedPlan || brand.currentPlan?._id || ''}
                        onChange={(e) => handlePlanChange(brand._id, e.target.value)}
                        className="bg-[#2a2a2a] border border-gray-600 text-white text-sm rounded-lg px-3 py-2 focus:ring-2 focus:ring-lime-500 focus:border-lime-500"
                      >
                        <option value="">Select Plan</option>
                        {plans.map((plan) => (
                          <option key={plan._id} value={plan._id}>
                            {plan.name}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        {formatDateRange(brand.currentStartDate, brand.currentEndDate)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="date"
                        value={brand.newValidity || brand.currentEndDate?.split('T')[0] || ''}
                        onChange={(e) => handleValidityChange(brand._id, e.target.value)}
                        className="bg-[#2a2a2a] border border-gray-600 text-white text-sm rounded-lg px-3 py-2 focus:ring-2 focus:ring-lime-500 focus:border-lime-500"
                      />
                    </td>
                    <td className="px-6 py-4 text-center">
                      <button
                        onClick={() => handleSave(brand._id)}
                        className="bg-lime-500 hover:bg-lime-600 text-black px-4 py-2 rounded-lg text-sm font-semibold transition-colors"
                      >
                        💾 Save
                      </button>
                    </td>
                    <td className="px-6 py-4 text-center">
                      <button
                        onClick={() => handleViewHistory(brand._id, brand.companyName)}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-colors"
                      >
                        🕘 View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {brands.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg">No brands found</p>
          </div>
        )}
      </div>

      {/* Plan History Modal */}
      {showHistoryModal && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
          <div className="bg-[#1a1a1a] rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-white">
                Plan History - {selectedBrandName}
              </h2>
              <button
                onClick={() => setShowHistoryModal(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-3">
              {selectedBrandHistory.length > 0 ? (
                selectedBrandHistory.map((entry, index) => (
                  <div key={index} className="bg-[#2a2a2a] p-4 rounded-lg">
                    <div className="flex items-center gap-2 text-sm text-gray-300">
                      <span className="text-lime-400">🔹</span>
                      <span>{formatDate(entry.date)}:</span>
                      <span className="font-semibold">
                        {entry.previousPlan} → {entry.newPlan}
                      </span>
                      <span>({entry.validityMonths} months)</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-400 text-center py-8">No plan history available</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BrandPlanManagement;
