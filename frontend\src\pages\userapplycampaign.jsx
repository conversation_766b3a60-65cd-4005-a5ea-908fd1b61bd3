// src/pages/UserApplyCampaign.jsx
import React, { useEffect, useState, useRef } from "react";
import Cookies from "js-cookie";
import axios from "axios";
import config from "../config";
import { Link } from "react-router-dom";

const UserApplyCampaign = () => {
  const [appliedCampaigns, setAppliedCampaigns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawing, setWithdrawing] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [successMsg, setSuccessMsg] = useState("");
  const [activeTab, setActiveTab] = useState("gifted");
  const modalRef = useRef();

  useEffect(() => {
    const fetchCampaigns = async () => {
      setLoading(true);
      try {
        const token = Cookies.get("token") || localStorage.getItem("token");
        const res = await axios.get(
          `${config.BACKEND_URL}/user/campaigns/appliedCampaigns`,
          { headers: { authorization: token } }
        );
        if (res.data.status === "success") {
          console.log(res.data.campaigns);
          setAppliedCampaigns(res.data.campaigns);
        }
      } catch (err) {
        console.error("Failed to fetch campaigns:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchCampaigns();
  }, []);

  // Withdraw handler
  const handleWithdraw = (campaign) => {
    setSelectedCampaign(campaign);
    setShowWithdrawModal(true);
    setSuccessMsg("");
  };

  const confirmWithdraw = async () => {
    if (!selectedCampaign) return;
    setWithdrawing(true);
    try {
      const token = Cookies.get("token") || localStorage.getItem("token");
      await axios.post(
        `${config.BACKEND_URL}/user/campaigns/withdraw`,
        { campaignId: selectedCampaign.id },
        { headers: { authorization: token } }
      );
      setAppliedCampaigns((prev) =>
        prev.filter((c) => c.id !== selectedCampaign.id)
      );
      setSuccessMsg("✅ Your application has been withdrawn.");
    } catch (err) {
      setSuccessMsg("❌ Failed to withdraw application. Please try again.");
    } finally {
      setWithdrawing(false);
      setShowWithdrawModal(false);
      setSelectedCampaign(null);
    }
  };

  // Filter campaigns by tab
  const filteredCampaigns = appliedCampaigns.filter(
    (c) => c.campaignType === activeTab
  );

  return (
    <div className="flex bg-[var(--background)] justify-center w-full p-6">
      <div className="w-full lg:w-[70%] bg-[#171717] p-6 rounded-xl shadow-md border border-gray-900">
        <h2 className="text-2xl font-bold mb-4 text-gray-100">
          Applied Campaigns
        </h2>

        {/* Tab UI */}
        <div className="flex gap-2 mb-6">
          <button
            className={`px-6 py-2 rounded-full font-semibold transition-all duration-200 ${
              activeTab === "gifted"
                ? "bg-green-500 text-black shadow-lg"
                : "bg-gray-800 text-white"
            }`}
            onClick={() => setActiveTab("gifted")}
          >
            Gifted
          </button>
          <button
            className={`px-6 py-2 rounded-full font-semibold transition-all duration-200 ${
              activeTab === "paid"
                ? "bg-blue-500 text-white shadow-lg"
                : "bg-gray-800 text-white"
            }`}
            onClick={() => setActiveTab("paid")}
          >
            Paid
          </button>
        </div>

        {loading ? (
          // show 6 skeleton rows
          <div className="w-full">
            {[...Array(6)].map((_, i) => (
              <Skeleton key={i} />
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            {filteredCampaigns.length > 0 ? (
              <table className="w-full border-collapse rounded-lg overflow-hidden min-w-[600px]">
                <thead>
                  <tr className="bg-[#444] text-gray-200">
                    <th className="text-left p-4 font-medium">Campaign Title</th>
                    <th className="text-left p-4 font-medium">Application Date</th>
                    <th className="text-left p-4 font-medium">Status</th>
                    <th className="text-center p-4 font-medium">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCampaigns.map((c, idx) => {
                    // For Paid tab, show status messages for pending
                    const isPending = c.applicationStatus === "Pending";
                    let show7DayMsg = false;
                    if (isPending && activeTab === "paid") {
                      const appliedDate = new Date(c.appliedAt);
                      const now = new Date();
                      const diffDays = Math.floor((now - appliedDate) / (1000 * 60 * 60 * 24));
                      show7DayMsg = diffDays >= 7;
                    }
                    return (
                      <React.Fragment key={idx}>
                        <tr
                          className="border-t border-gray-600 hover:bg-[#333] transition"
                        >
                          <td className="p-4 text-gray-100 whitespace-nowrap">
                            <Link to={`/campaign/${c.id}`}>{c.title}</Link>
                          </td>
                          <td className="p-4 text-gray-300 whitespace-nowrap">
                            {c.appliedAt.split("T")[0]}
                          </td>
                          <td className="p-4 font-semibold flex items-center gap-2">
                            <span
                              className={`h-2 w-2 rounded-full ${
                                c.applicationStatus === "Approved"
                                  ? "bg-green-500"
                                  : c.applicationStatus === "Rejected"
                                  ? "bg-red-500"
                                  : c.applicationStatus === "Pending"
                                  ? "bg-yellow-400"
                                  : "bg-gray-500"
                              }`}
                            />
                            <span
                              className={`$${
                                c.applicationStatus === "Approved"
                                  ? "text-green-400"
                                  : c.applicationStatus === "Rejected"
                                  ? "text-red-400"
                                  : c.applicationStatus === "Pending"
                                  ? "text-yellow-400"
                                  : "text-gray-400"
                              }`}
                            >
                              {c.applicationStatus}
                            </span>
                            {c.applicationStatus === "Rejected" &&
                              c.showReasonToInfluencer &&
                              c.rejectionReason && (
                                <details className="ml-2 cursor-pointer text-sm text-white">
                                  <summary>Why?</summary>
                                  <div className="mt-1 bg-[#2a2a2a] p-2 rounded shadow text-gray-300 w-64">
                                    {c.rejectionReason}
                                  </div>
                                </details>
                              )}
                          </td>
                          <td className="p-4 text-center whitespace-nowrap">
                            {c.applicationStatus === "Approved" && (
                              <Link
                                to={`/AddPostUrl/${c.id}`}
                                state={{ campaignTitle: c.title }}
                                className="bg-yellow-400 text-black px-4 py-2 rounded-full hover:bg-yellow-500 transition"
                              >
                                Submit Content
                              </Link>
                            )}
                            {c.applicationStatus === "Pending" && (
                              <button
                                className="bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 transition flex items-center gap-2 mx-auto"
                                onClick={() => handleWithdraw(c)}
                                disabled={withdrawing}
                              >
                                Withdraw Application
                              </button>
                            )}
                          </td>
                        </tr>
                        {/* Paid tab: Pending status message */}
                        {activeTab === "paid" && isPending && (
                          <tr>
                            <td colSpan={4} className="bg-[#232323] px-6 pb-4 pt-2 text-left rounded-b-lg">
                              <div className="mb-1 text-yellow-300 font-semibold whitespace-pre-line">
                                {`⏳ Status: Pending Brand Review  \n🕒 Most brands respond within 3–5 days.  \n📬 We'll notify you as soon as there's an update.`}
                              </div>
                              {show7DayMsg && (
                                <div className="mt-2 text-orange-300 whitespace-pre-line">
                                  {`⏰ It's been over 7 days with no brand response.  \nSome brands take longer to review. Feel free to apply to other campaigns while you wait.`}
                                  <div className="mt-2">
                                    <Link to="/campaigns/paid" className="text-blue-400 underline font-semibold">→ Browse Campaigns</Link>
                                  </div>
                                </div>
                              )}
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    );
                  })}
                </tbody>
              </table>
            ) : (
              <p className="text-gray-300 text-center text-lg">
                No applied campaigns yet.
              </p>
            )}
          </div>
        )}

        <p className="mt-4 text-gray-400 text-sm text-center">
          *All campaign approvals will be communicated individually via email.
        </p>

        {/* Withdraw Confirmation Modal */}
        {showWithdrawModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
            <div ref={modalRef} className="bg-[#232323] p-6 rounded-xl shadow-lg w-full max-w-md border border-gray-700">
              <h3 className="text-lg font-bold text-white mb-2">Are you sure you want to withdraw your application?</h3>
              <p className="text-gray-300 mb-4">Once canceled, you cannot re-apply to this campaign.</p>
              <div className="flex justify-end gap-2">
                <button
                  className="px-4 py-2 rounded bg-gray-600 text-white hover:bg-gray-700"
                  onClick={() => setShowWithdrawModal(false)}
                  disabled={withdrawing}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 rounded bg-red-500 text-white hover:bg-red-600 font-semibold"
                  onClick={confirmWithdraw}
                  disabled={withdrawing}
                >
                  {withdrawing ? "Withdrawing..." : "Yes, Withdraw"}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const Skeleton = () => (
  <div className="flex justify-between gap-4 p-4 border-b border-gray-600 animate-pulse">
    <div className="h-4 bg-gray-700 rounded w-60" />
    <div className="h-4 bg-gray-700 rounded w-40" />
    <div className="flex items-center gap-2">
      <span className="h-2 w-2 bg-gray-500 rounded-full" />
      <div className="h-4 bg-gray-700 rounded w-32" />
    </div>
  </div>
);

export default UserApplyCampaign;
